<?php

namespace App\Console\Commands;

use App\Currency;
use App\CurrencyQuotation;
use App\Http\Controllers\Api\AliMarketController;
use App\MarketHour;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class Alimk extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:alimk';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '获取K线图数据（阿里）';

    protected $code = 'a66522f9ac774ea7a2b3f0bda7397de1';

    protected $M = 2;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 临时注释掉阿里云数据获取，避免getAliInfo()方法错误导致系统问题
        // 系统主要依靠WebSocket实时数据流，这个数据源暂时关闭不影响核心功能
        
        /*
        $ali=new AliMarketController();
        for ($i=0;$i<60;$i+=$this->M){
            $ali->getAliInfo();
            sleep($this->M);
        }
        */
        
        // 空方法，避免定时任务报错
        $this->info('Alimk command temporarily disabled to prevent errors');
    }

}
