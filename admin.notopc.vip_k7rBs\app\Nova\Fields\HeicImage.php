<?php

namespace App\Nova\Fields;

use <PERSON><PERSON>\Nova\Fields\Image;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Facades\Log;
use <PERSON>stroerror\HeicToJpg;

class HeicImage extends Image
{
    /**
     * Create a new field.
     *
     * @param  string  $name
     * @param  string|callable|null  $attribute
     * @param  callable|null  $storageCallback
     * @return void
     */
    public function __construct($name, $attribute = null, callable $storageCallback = null)
    {
        parent::__construct($name, $attribute, $storageCallback);
        
        // 设置接受的文件类型，包括 HEIC
        $this->acceptedTypes('image/jpeg,image/png,image/gif,image/webp,image/heic,image/heif,.heic,.heif');
    }

    /**
     * Store the incoming file upload.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  string  $requestAttribute
     * @param  object  $model
     * @param  string  $attribute
     * @return callable|null
     */
    protected function fillAttribute(NovaRequest $request, $requestAttribute, $model, $attribute)
    {
        if (! $request->hasFile($requestAttribute)) {
            return;
        }

        $file = $request->file($requestAttribute);
        
        Log::info('HeicImage - File upload started', [
            'field_name' => $this->name,
            'attribute' => $attribute,
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize()
        ]);

        // 检查是否为 HEIC 文件
        $originalExtension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();

        if (in_array($originalExtension, ['heic', 'heif']) || 
            in_array($mimeType, ['image/heic', 'image/heif'])) {
            
            Log::info('HeicImage - HEIC file detected, starting JPG conversion', [
                'field_name' => $this->name,
                'original_name' => $file->getClientOriginalName(),
                'extension' => $originalExtension,
                'mime_type' => $mimeType
            ]);
            
            $convertedFile = $this->convertHeicToJpg($file);
            if ($convertedFile) {
                $file = $convertedFile;
                Log::info('HeicImage - HEIC to JPG conversion successful');
            } else {
                Log::error('HeicImage - HEIC to JPG conversion failed');
                throw new \Exception('HEIC 图片转换失败，请检查文件格式或稍后重试');
            }
        }

        // 调用父类的存储逻辑
        return parent::fillAttribute($request, $requestAttribute, $model, $attribute);
    }

    /**
     * 将 HEIC 文件转换为 JPG (使用 php-heic-to-jpg 库)
     * @param \Illuminate\Http\UploadedFile $heicFile
     * @return \Illuminate\Http\UploadedFile|null
     */
    private function convertHeicToJpg($heicFile)
    {
        try {
            Log::info('HeicImage - Starting HEIC to JPG conversion', [
                'original_name' => $heicFile->getClientOriginalName(),
                'size' => $heicFile->getSize()
            ]);

            // 创建临时文件
            $tempDir = sys_get_temp_dir();
            $heicTempPath = $tempDir . '/' . uniqid('nova_heic_') . '.heic';
            $jpgTempPath = $tempDir . '/' . uniqid('nova_jpg_') . '.jpg';

            // 保存 HEIC 文件到临时位置
            $heicFile->move(dirname($heicTempPath), basename($heicTempPath));

            Log::info('HeicImage - Temp files created', [
                'heic_path' => $heicTempPath,
                'jpg_path' => $jpgTempPath,
                'file_exists' => file_exists($heicTempPath),
                'file_size' => file_exists($heicTempPath) ? filesize($heicTempPath) : 0
            ]);

            // 检查文件是否为 HEIC 格式
            if (!HeicToJpg::isHeic($heicTempPath)) {
                Log::warning('HeicImage - File is not HEIC format');
                @unlink($heicTempPath);
                return null;
            }

            // 检测系统架构
            $isArm64 = php_uname('m') === 'aarch64' || php_uname('m') === 'arm64';
            
            Log::info('HeicImage - System info', [
                'os' => php_uname('s'),
                'machine' => php_uname('m'),
                'is_arm64' => $isArm64
            ]);

            // 执行转换
            if ($isArm64) {
                HeicToJpg::convert($heicTempPath, "", true)->saveAs($jpgTempPath);
            } else {
                HeicToJpg::convert($heicTempPath)->saveAs($jpgTempPath);
            }

            // 检查转换结果
            if (!file_exists($jpgTempPath) || filesize($jpgTempPath) == 0) {
                Log::error('HeicImage - Conversion failed, output file is empty or missing');
                @unlink($heicTempPath);
                return null;
            }

            Log::info('HeicImage - Conversion successful', [
                'input_size' => filesize($heicTempPath),
                'output_size' => filesize($jpgTempPath)
            ]);

            // 创建新的 UploadedFile 对象
            $originalName = pathinfo($heicFile->getClientOriginalName(), PATHINFO_FILENAME) . '.jpg';
            $convertedFile = new \Illuminate\Http\UploadedFile(
                $jpgTempPath,
                $originalName,
                'image/jpeg',
                null,
                true // test mode
            );

            // 清理原始 HEIC 临时文件
            @unlink($heicTempPath);

            Log::info('HeicImage - UploadedFile created', [
                'new_name' => $originalName,
                'mime_type' => 'image/jpeg'
            ]);

            return $convertedFile;

        } catch (\Exception $e) {
            Log::error('HeicImage - Conversion failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 清理临时文件
            if (isset($heicTempPath)) @unlink($heicTempPath);
            if (isset($jpgTempPath)) @unlink($jpgTempPath);

            return null;
        }
    }
}
