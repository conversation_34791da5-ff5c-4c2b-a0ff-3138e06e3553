<?php

namespace App\Nova\Fields;

use <PERSON><PERSON>\Nova\Fields\Image;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class HeicImage extends Image
{
    /**
     * Create a new field.
     *
     * @param  string  $name
     * @param  string|callable|null  $attribute
     * @param  callable|null  $storageCallback
     * @return void
     */
    public function __construct($name, $attribute = null, callable $storageCallback = null)
    {
        parent::__construct($name, $attribute, $storageCallback);
        
        // 设置接受的文件类型，包括 HEIC
        $this->acceptedTypes('image/jpeg,image/png,image/gif,image/webp,image/heic,image/heif,.heic,.heif');
    }

    /**
     * Store the incoming file upload.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  string  $requestAttribute
     * @param  object  $model
     * @param  string  $attribute
     * @return callable|null
     */
    protected function fillAttribute(NovaRequest $request, $requestAttribute, $model, $attribute)
    {
        if (! $request->hasFile($requestAttribute)) {
            return;
        }

        $file = $request->file($requestAttribute);
        
        Log::info('HeicImage - File upload started', [
            'field_name' => $this->name,
            'attribute' => $attribute,
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize()
        ]);

        // 检查是否为 HEIC 文件
        $originalExtension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();

        if (in_array($originalExtension, ['heic', 'heif']) ||
            in_array($mimeType, ['image/heic', 'image/heif'])) {

            Log::info('HeicImage - HEIC file detected, starting JPG conversion', [
                'field_name' => $this->name,
                'original_name' => $file->getClientOriginalName(),
                'extension' => $originalExtension,
                'mime_type' => $mimeType
            ]);

            $convertedFile = $this->convertHeicToJpg($file);
            if ($convertedFile) {
                $file = $convertedFile;
                Log::info('HeicImage - HEIC to JPG conversion successful');
            } else {
                Log::error('HeicImage - HEIC to JPG conversion failed');
                throw new \Exception('HEIC 图片转换失败，请将图片转换为 JPG 格式后重新上传');
            }
        }

        // 调用父类的存储逻辑
        return parent::fillAttribute($request, $requestAttribute, $model, $attribute);
    }

    /**
     * 将 HEIC 文件转换为 JPG
     * @param \Illuminate\Http\UploadedFile $heicFile
     * @return \Illuminate\Http\UploadedFile|null
     */
    private function convertHeicToJpg($heicFile)
    {
        try {
            Log::info('HeicImage - Starting HEIC to JPG conversion', [
                'original_name' => $heicFile->getClientOriginalName(),
                'size' => $heicFile->getSize()
            ]);

            // 检查 ImageMagick 扩展
            if (!extension_loaded('imagick')) {
                Log::error('HeicImage - ImageMagick extension not loaded');
                return null;
            }

            // 创建临时文件
            $tempDir = sys_get_temp_dir();
            $heicTempPath = $tempDir . '/' . uniqid('nova_heic_') . '.heic';
            $jpgTempPath = $tempDir . '/' . uniqid('nova_jpg_') . '.jpg';

            // 保存 HEIC 文件到临时位置
            $heicFile->move(dirname($heicTempPath), basename($heicTempPath));

            Log::info('HeicImage - Temp files created', [
                'heic_path' => $heicTempPath,
                'jpg_path' => $jpgTempPath
            ]);

            // 创建 Imagick 对象并加载 HEIC 文件
            $image = new \Imagick($heicTempPath);

            // 设置输出格式为 JPEG
            $image->setImageFormat('jpeg');

            // 设置 JPEG 质量
            $image->setImageCompressionQuality(85);

            // 获取原始尺寸
            $originalWidth = $image->getImageWidth();
            $originalHeight = $image->getImageHeight();

            Log::info('HeicImage - Image loaded', [
                'width' => $originalWidth,
                'height' => $originalHeight
            ]);

            // 如果图片太大，进行压缩
            $maxWidth = 2048;
            $maxHeight = 2048;
            if ($originalWidth > $maxWidth || $originalHeight > $maxHeight) {
                $image->thumbnailImage($maxWidth, $maxHeight, true);
                Log::info('HeicImage - Image resized', [
                    'new_width' => $image->getImageWidth(),
                    'new_height' => $image->getImageHeight()
                ]);
            }

            // 保存为 JPEG 文件
            $image->writeImage($jpgTempPath);

            // 销毁 Imagick 对象以释放资源
            $image->destroy();

            // 检查转换结果
            if (!file_exists($jpgTempPath) || filesize($jpgTempPath) == 0) {
                Log::error('HeicImage - Converted file is empty or does not exist');
                @unlink($heicTempPath);
                return null;
            }

            Log::info('HeicImage - Conversion successful', [
                'converted_size' => filesize($jpgTempPath)
            ]);

            // 创建新的 UploadedFile 对象
            $originalName = pathinfo($heicFile->getClientOriginalName(), PATHINFO_FILENAME) . '.jpg';
            $convertedFile = new \Illuminate\Http\UploadedFile(
                $jpgTempPath,
                $originalName,
                'image/jpeg',
                null,
                true // test mode
            );

            // 清理原始 HEIC 临时文件
            @unlink($heicTempPath);

            Log::info('HeicImage - UploadedFile created', [
                'new_name' => $originalName,
                'mime_type' => 'image/jpeg'
            ]);

            return $convertedFile;

        } catch (\Exception $e) {
            Log::error('HeicImage - Conversion failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 清理临时文件
            if (isset($heicTempPath)) @unlink($heicTempPath);
            if (isset($jpgTempPath)) @unlink($jpgTempPath);

            return null;
        }
    }

    /**
     * 将 HEIC 文件转换为 PNG (保留原方法)
     * @param \Illuminate\Http\UploadedFile $heicFile
     * @return \Illuminate\Http\UploadedFile|null
     */
    private function convertHeicToPng($heicFile)
    {
        try {
            Log::info('HeicImage - Starting HEIC conversion', [
                'original_name' => $heicFile->getClientOriginalName(),
                'size' => $heicFile->getSize()
            ]);

            // 检查 ImageMagick 扩展
            if (!extension_loaded('imagick')) {
                Log::error('HeicImage - ImageMagick extension not loaded');
                return null;
            }

            // 检查 ImageMagick 是否支持 HEIC
            $imagick = new \Imagick();
            $formats = $imagick->queryFormats();
            if (!in_array('HEIC', $formats) && !in_array('HEIF', $formats)) {
                Log::error('HeicImage - ImageMagick does not support HEIC format');
                return null;
            }

            // 创建临时文件
            $tempDir = sys_get_temp_dir();
            $tempHeicPath = tempnam($tempDir, 'nova_heic_') . '.heic';
            $tempPngPath = tempnam($tempDir, 'nova_png_') . '.png';

            // 保存 HEIC 文件到临时位置
            $heicFile->move(dirname($tempHeicPath), basename($tempHeicPath));

            Log::info('HeicImage - Temp files created', [
                'heic_path' => $tempHeicPath,
                'png_path' => $tempPngPath
            ]);

            // 使用 ImageMagick 转换
            $image = new \Imagick($tempHeicPath);
            
            // 获取原始尺寸
            $originalWidth = $image->getImageWidth();
            $originalHeight = $image->getImageHeight();
            
            Log::info('HeicImage - Image loaded', [
                'width' => $originalWidth,
                'height' => $originalHeight
            ]);

            // 设置输出格式为 PNG
            $image->setImageFormat('png');
            $image->setImageCompressionQuality(90);

            // 如果图片太大，进行压缩
            $maxWidth = 2048;
            $maxHeight = 2048;
            if ($originalWidth > $maxWidth || $originalHeight > $maxHeight) {
                $image->thumbnailImage($maxWidth, $maxHeight, true);
                Log::info('HeicImage - Image resized', [
                    'new_width' => $image->getImageWidth(),
                    'new_height' => $image->getImageHeight()
                ]);
            }

            // 保存转换后的文件
            $image->writeImage($tempPngPath);
            $image->clear();
            $image->destroy();

            // 检查转换结果
            if (!file_exists($tempPngPath) || filesize($tempPngPath) == 0) {
                Log::error('HeicImage - Converted file is empty or does not exist');
                @unlink($tempHeicPath);
                @unlink($tempPngPath);
                return null;
            }

            Log::info('HeicImage - Conversion successful', [
                'converted_size' => filesize($tempPngPath)
            ]);

            // 创建新的 UploadedFile 对象
            $originalName = pathinfo($heicFile->getClientOriginalName(), PATHINFO_FILENAME) . '.png';
            $convertedFile = new \Illuminate\Http\UploadedFile(
                $tempPngPath,
                $originalName,
                'image/png',
                null,
                true // test mode
            );

            // 清理原始 HEIC 临时文件
            @unlink($tempHeicPath);

            Log::info('HeicImage - UploadedFile created', [
                'new_name' => $originalName,
                'mime_type' => 'image/png'
            ]);

            return $convertedFile;

        } catch (\Exception $e) {
            Log::error('HeicImage - Conversion failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 清理临时文件
            if (isset($tempHeicPath)) @unlink($tempHeicPath);
            if (isset($tempPngPath)) @unlink($tempPngPath);

            return null;
        }
    }
}
