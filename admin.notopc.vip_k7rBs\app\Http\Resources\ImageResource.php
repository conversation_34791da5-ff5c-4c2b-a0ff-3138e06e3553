<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $data = parent::toArray($request);

        // 如果有 path 字段，生成支持 HEIC 的 URL
        if (isset($data['path'])) {
            $data['url'] = heic_image_url($data['path'], $data['disk'] ?? 'public');
        }

        return $data;
    }
}
