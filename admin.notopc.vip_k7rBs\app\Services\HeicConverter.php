<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Exception;

/**
 * HEIC 转 JPG 转换服务
 * 基于 php-heic-to-jpg 库的简化实现
 */
class HeicConverter
{
    private $binPath;
    private $isArm64;

    public function __construct()
    {
        $this->isArm64 = php_uname('m') === 'aarch64' || php_uname('m') === 'arm64';
        $this->binPath = $this->getBinaryPath();
    }

    /**
     * 获取二进制文件路径
     */
    private function getBinaryPath()
    {
        $basePath = '/www/wwwroot/php-heic-to-jpg/bin';
        
        // 根据系统选择合适的二进制文件
        if (PHP_OS_FAMILY === 'Darwin') {
            // macOS
            return $this->isArm64 ? 
                $basePath . '/php-heic-to-jpg-mac-arm64' : 
                $basePath . '/php-heic-to-jpg-mac';
        } else {
            // Linux
            return $this->isArm64 ? 
                $basePath . '/php-heic-to-jpg-linux-arm64' : 
                $basePath . '/php-heic-to-jpg-linux';
        }
    }

    /**
     * 检查文件是否为 HEIC 格式
     */
    public function isHeic($filePath)
    {
        if (!file_exists($filePath)) {
            return false;
        }

        // 检查文件扩展名
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (in_array($extension, ['heic', 'heif'])) {
            return true;
        }

        // 检查文件头
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            return false;
        }

        $header = fread($handle, 12);
        fclose($handle);

        // HEIC 文件的魔术字节
        return strpos($header, 'ftyp') !== false && 
               (strpos($header, 'heic') !== false || strpos($header, 'mif1') !== false);
    }

    /**
     * 转换 HEIC 文件为 JPG
     */
    public function convert($inputPath, $outputPath = null)
    {
        try {
            Log::info('HeicConverter - Starting conversion', [
                'input_path' => $inputPath,
                'output_path' => $outputPath,
                'binary_path' => $this->binPath,
                'is_arm64' => $this->isArm64
            ]);

            // 检查输入文件
            if (!file_exists($inputPath)) {
                throw new Exception("输入文件不存在: {$inputPath}");
            }

            if (!$this->isHeic($inputPath)) {
                throw new Exception("文件不是 HEIC 格式: {$inputPath}");
            }

            // 检查二进制文件
            if (!file_exists($this->binPath)) {
                throw new Exception("转换器二进制文件不存在: {$this->binPath}");
            }

            if (!is_executable($this->binPath)) {
                // 尝试设置执行权限
                chmod($this->binPath, 0755);
                if (!is_executable($this->binPath)) {
                    throw new Exception("转换器二进制文件不可执行: {$this->binPath}");
                }
            }

            // 如果没有指定输出路径，生成一个
            if (!$outputPath) {
                $outputPath = sys_get_temp_dir() . '/' . uniqid('heic_converted_') . '.jpg';
            }

            // 构建命令
            $command = escapeshellarg($this->binPath) . ' ' . 
                      escapeshellarg($inputPath) . ' ' . 
                      escapeshellarg($outputPath);

            Log::info('HeicConverter - Executing command', [
                'command' => $command
            ]);

            // 执行转换
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            Log::info('HeicConverter - Command executed', [
                'return_code' => $returnCode,
                'output' => implode("\n", $output)
            ]);

            // 检查执行结果
            if ($returnCode !== 0) {
                throw new Exception("转换失败，返回码: {$returnCode}, 输出: " . implode("\n", $output));
            }

            // 检查输出文件
            if (!file_exists($outputPath) || filesize($outputPath) == 0) {
                throw new Exception("转换失败，输出文件为空或不存在: {$outputPath}");
            }

            Log::info('HeicConverter - Conversion successful', [
                'input_size' => filesize($inputPath),
                'output_size' => filesize($outputPath),
                'output_path' => $outputPath
            ]);

            return $outputPath;

        } catch (Exception $e) {
            Log::error('HeicConverter - Conversion failed', [
                'error' => $e->getMessage(),
                'input_path' => $inputPath,
                'output_path' => $outputPath
            ]);
            throw $e;
        }
    }

    /**
     * 转换 HEIC 文件并返回二进制数据
     */
    public function convertToData($inputPath)
    {
        $tempOutput = $this->convert($inputPath);
        
        try {
            $data = file_get_contents($tempOutput);
            @unlink($tempOutput); // 清理临时文件
            return $data;
        } catch (Exception $e) {
            @unlink($tempOutput); // 清理临时文件
            throw $e;
        }
    }

    /**
     * 获取系统信息
     */
    public function getSystemInfo()
    {
        return [
            'os' => PHP_OS_FAMILY,
            'machine' => php_uname('m'),
            'is_arm64' => $this->isArm64,
            'binary_path' => $this->binPath,
            'binary_exists' => file_exists($this->binPath),
            'binary_executable' => file_exists($this->binPath) && is_executable($this->binPath)
        ];
    }
}
