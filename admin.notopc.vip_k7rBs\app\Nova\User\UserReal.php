<?php

namespace App\Nova\User;

use App\Nova\Resource;
use <PERSON><PERSON><PERSON>\NovaToggle\Toggle;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class UserReal extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\UserReal>
     */
    public static $model = \App\Models\UserReal::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static function group(){
        return __('User');
    }


    /**
     * Custom priority level of the resource.
     *
     * @var int
     */
    public static $priority =9999;

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->where('simulation', 0);

    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make(__('Email'), 'account')->readonly(),

            Text::make(__('RealName'), 'name')->readonly(),

            Text::make(__('ID Number'), 'card_id')->readonly(),

            Text::make(__('Time Of Application'),'create_time')->readonly(),

            Select::make(__('Status'),'review_status')->options([
                3 => '审核失败',
                2 => '审核通过',
                1 => '审核中',
            ])->displayUsingLabels(),
            Image::make(__('FrontPic'), 'front_pic')
                ->detailWidth(800)
                ->displayUsing(function ($value) {
                    return $value ? heic_image_url($value) : null;
                }),

            Image::make(__('ReversePic'), 'reverse_pic')
                ->detailWidth(800)
                ->displayUsing(function ($value) {
                    return $value ? heic_image_url($value) : null;
                }),

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the displayble label of the resource.
     *
     * @return string
     */
    public static function label()
    {
        return __('Users Reals');
    }

    /**
     * Get the displayble singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('Users Reals');
    }

    /**
     * Get the value that should be displayed to represent the resource.
     *
     * @return string
     */
    public function title()
    {
        return $this->name;
    }
}
