<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ElasticsearchHealthService;

class ElasticsearchHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'elasticsearch:health {action?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Elasticsearch health status and manage degradation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->showStatus();
                break;
            case 'mark-healthy':
                ElasticsearchHealthService::markHealthy();
                $this->info('Elasticsearch marked as healthy');
                break;
            case 'mark-unhealthy':
                ElasticsearchHealthService::markUnhealthy();
                $this->warn('Elasticsearch marked as unhealthy - system will use MySQL fallback');
                break;
            case 'check':
            default:
                $this->performCheck();
                break;
        }
    }

    private function showStatus()
    {
        $status = ElasticsearchHealthService::getStatus();
        
        $this->info('Elasticsearch Health Status:');
        $this->table(
            ['Property', 'Value'],
            [
                ['Healthy', $status['is_healthy'] ? 'Yes' : 'No'],
                ['Failure Count', $status['failure_count']],
                ['Failure Threshold', $status['threshold']],
                ['Last Check', $status['last_check']],
            ]
        );

        if (!$status['is_healthy']) {
            $this->warn('⚠️  Elasticsearch is unhealthy - system is using MySQL fallback');
        } else {
            $this->info('✅ Elasticsearch is healthy');
        }
    }

    private function performCheck()
    {
        $this->info('Performing Elasticsearch health check...');
        
        $isHealthy = ElasticsearchHealthService::isHealthy();
        
        if ($isHealthy) {
            $this->info('✅ Elasticsearch is healthy');
        } else {
            $this->warn('⚠️  Elasticsearch is unhealthy - check logs for details');
        }
        
        $this->showStatus();
    }
}
