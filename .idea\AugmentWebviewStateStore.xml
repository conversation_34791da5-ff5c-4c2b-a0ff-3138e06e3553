<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;dbe5681e-6000-424d-99ce-4ce5a9986656&quot;,&quot;conversations&quot;:{&quot;dbe5681e-6000-424d-99ce-4ce5a9986656&quot;:{&quot;id&quot;:&quot;dbe5681e-6000-424d-99ce-4ce5a9986656&quot;,&quot;createdAtIso&quot;:&quot;2025-08-21T00:35:59.284Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-21T00:58:53.675Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;1428fbf4-8acc-4b49-b4d6-87ed222db4bd&quot;,&quot;request_message&quot;:&quot;#/pages/stock/stock 看看 我这个页面的顶部价格和K线价格显示不一致 看看怎么能让他们同步 可以参考下 store.md\n\n\n\n&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;#/pages/stock/stock 看看 我这个页面的顶部价格和K线价格显示不一致 看看怎么能让他们同步 可以参考下 store.md&quot;}]},{&quot;type&quot;:&quot;file&quot;,&quot;attrs&quot;:{&quot;src&quot;:&quot;2a79a9866fede2a0189f7674cc4c608f8cad1be263cf5a60c708eb9791a402b8.png&quot;,&quot;alt&quot;:null,&quot;title&quot;:&quot;image.png&quot;,&quot;renderMode&quot;:&quot;collapsed&quot;,&quot;isLoading&quot;:false,&quot;fileSizeBytes&quot;:133352,&quot;mimeType&quot;:&quot;image/png&quot;}},{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. You must upgrade to paid plan to continue using Augment. [Purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;#/pages/stock/stock 看看 我这个页面的顶部价格和K线价格显示不一致 看看怎么能让他们同步 可以参考下 store.md&quot;}},{&quot;id&quot;:1,&quot;type&quot;:3,&quot;image_id_node&quot;:{&quot;image_id&quot;:&quot;2a79a9866fede2a0189f7674cc4c608f8cad1be263cf5a60c708eb9791a402b8.png&quot;,&quot;format&quot;:1}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/Code_Projects/btc/notopc_vip&quot;,&quot;folderRoot&quot;:&quot;D:/Code_Projects/btc/notopc_vip&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-21T00:58:53.675Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. You must upgrade to paid plan to continue using Augment. [Purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-a6affa7e-0137-494c-8f7b-99c1d2649c89&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;fc30bfcc-818f-465b-9bd5-e3219956f9c1&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>