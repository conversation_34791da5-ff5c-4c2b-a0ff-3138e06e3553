<?php

namespace App\Models;

use App\Models\Currency;
use App\Models\CurrencyMatch;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Elasticsearch\ClientBuilder;
use App\Jobs\SendMarket;

class MarketHour extends Model
{
    protected $table = 'market_hour';
    public $timestamps = false;

    protected static $esClient = null;
    protected static $clientCreatedAt = null;

    protected static $period = [
        5 => "1min",
        6 => "5min",
        1 => "15min",
        7 => "30min",
        2 => "60min",
        3 => "1hour",
        4 => "1day",
        8 => "1week",
        9 => "1mon",
        10 => "1year",
    ];

    /**
     * 获得一个ElasticsearchClient实例
     *
     * @return \Elasticsearch\Client
     */
    public static function getEsearchClient()
    {
        // 优化：减少连接创建频率，添加连接池管理
        if (is_null(self::$esClient) || !self::isClientHealthy()) {
            // 从配置文件获取ES连接配置
            $config = config('elasticsearch.connections.default');

            $hosts = $config['hosts'] ?? ['http://127.0.0.1:9200'];

            $clientBuilder = ClientBuilder::create()->setHosts($hosts);

            // Elasticsearch 7.x API 兼容配置
            // 设置连接参数（7.x 版本的正确方式）- 优化高并发性能
            $clientBuilder->setConnectionParams([
                'timeout' => $config['timeout'] ?? 2,        // 从5秒减少到2秒，快速失败
                'connect_timeout' => $config['connect_timeout'] ?? 1,  // 从2秒减少到1秒
                'verify' => $config['ssl_verification'] ?? false,
                'max_handles' => 100,                        // 增加连接池大小
                'persistent' => true,                        // 启用持久连接
            ]);

            // 设置重试次数（7.x 版本支持）
            if (method_exists($clientBuilder, 'setRetries')) {
                $clientBuilder->setRetries($config['retries'] ?? 1);
            }

            // SSL配置（如果需要）
            if (!empty($config['ssl_verification']) && $config['ssl_verification'] === false) {
                $clientBuilder->setSSLVerification(false);
            }

            self::$esClient = $clientBuilder->build();

            // 记录连接创建时间
            self::$clientCreatedAt = time();
        }
        return self::$esClient;
    }

    /**
     * 检查 ES 客户端是否健康
     * 避免长时间连接导致的超时问题
     */
    private static function isClientHealthy()
    {
        // 如果客户端不存在，肯定不健康
        if (is_null(self::$esClient)) {
            return false;
        }

        // 使用配置的连接生命周期（临时延长到2小时减少重连）
        $maxAge = config('elasticsearch.connections.default.connection_max_age', 7200);
        if (isset(self::$clientCreatedAt) && (time() - self::$clientCreatedAt) > $maxAge) {
            \Log::info('ES client connection expired, recreating...', [
                'age_seconds' => time() - self::$clientCreatedAt,
                'max_age' => $maxAge
            ]);
            self::$esClient = null;
            return false;
        }

        // ES 7.x 兼容的ping测试（不使用timeout参数）
        try {
            $result = self::$esClient->ping();
            return $result;
        } catch (\Exception $e) {
            \Log::warning('ES client health check failed, recreating connection', [
                'error' => $e->getMessage()
            ]);
            self::$esClient = null;
            return false;
        }
    }

    /**
     * 批量写入行情数据
     *
     * @param integer $currency_id 币种ID
     * @param integer $legal_id 法币ID
     * @param float $num 成交数量
     * @param float $price 成交价
     * @param integer $sign 来源标记[0.默认,1.交易更新,2.接口,3.后台添加
     * @param integer|null $time 时间戳
     * @param bool $cumulation 是否累计交易量,默认累计
     * @return void
     */
    public static function batchWriteMarketData($currency_id, $legal_id, $num, $price, $sign = 0, $time = null, $cumulation = true)
    {
        //$type类型:1.15分钟,2.1小时,3.4小时,4.一天,5.分时,6.5分钟，7.30分钟,8.一周,9.一月,10.一年
        empty($time) && $time = time();
        $types = [5, 6, 1, 7, 2, 3, 4, 8, 9, 10];
        $start = microtime(true);
        //写入行情数据
        DB::beginTransaction();
        foreach ($types as $key => $value) {
            $data = [];
            $timeline = self::getTimelineInstance($value, $currency_id, $legal_id, $sign, $time);
            bc_comp($timeline->start_price, 0) <= 0 && $data['start_price'] = $price;
            $data['end_price'] = $price;
            bc_comp($timeline->highest, $price) < 0 && $data['highest'] = $price;
            if (bc_comp($timeline->mminimum, 0) <= 0 || bc_comp($timeline->mminimum, $price) > 0) {
                $data['mminimum'] = $price;
            }
            $data['number'] = $cumulation ? bc_add($timeline->number, $num, 5) : $num;
            $result = $timeline->updateTimelineData($data);
            unset($timeline);
            unset($data);
        }
        DB::commit();
        $end = microtime(true);
        // echo '本次插入执行'. ($end - $start) . '秒';
    }


    /**
     * 批量写入行情数据
     *
     * @param integer $currency_id 币种ID
     * @param integer $legal_id 法币ID
     * @param array $market_data 行情数据
     * @param integer $sign 来源标记[0.默认,1.交易更新,2.接口,3.后台添加
     * @param integer|null $time 时间戳
     * @return void
     */
    public static function batchWriteKlineMarket($currency_id, $legal_id, $market_data, $sign = 0, $time = null)
    {
        //$type类型:1.15分钟,2.1小时,3.4小时,4.一天,5.分时,6.5分钟，7.30分钟,8.一周,9.一月,10.一年
        empty($time) && $time = time();
        $types = [5, 6, 1, 7, 2, 3, 4, 8, 9, 10];
        $start = microtime(true);
        //写入行情数据
        DB::beginTransaction();
        foreach ($types as $key => $value) {
            $data = [];
            $timeline = self::getTimelineInstance($value, $currency_id, $legal_id, $sign, $time);
            if ($value == 5) {
                //1分钟的只要传了就更新
                isset($market_data['open']) && $data['start_price'] = $market_data['open'];
                isset($market_data['close']) && $data['end_price'] = $market_data['close'];
                isset($market_data['high']) && $data['highest'] = $market_data['high'];
                isset($market_data['low']) && $data['mminimum'] = $market_data['low'];
                isset($market_data['amount']) && $data['number'] = $market_data['amount'];
            } else {
                if (isset($market_data['open']) && bc_comp($timeline->start_price, 0) <= 0) {
                    $data['start_price'] = $market_data['open'];
                }
                if (isset($market_data['close'])) {
                    $data['end_price'] = $market_data['close'];
                }
                if (isset($market_data['high']) && bc_comp($timeline->highest, $market_data['high']) < 0) {
                    $data['highest'] = $market_data['high'];
                }
                if (isset($market_data['low']) && (bc_comp($timeline->mminimum, 0) <= 0 || bc_comp($timeline->mminimum, $market_data['low']) > 0)) {
                    $data['mminimum'] = $market_data['low'];
                }
                if (isset($market_data['amount'])) {
                    $sum = self::where('type', 5)
                        ->where('currency_id', $currency_id)
                        ->where('legal_id', $legal_id)
                        ->where('day_time', '>=', $timeline->day_time)
                        ->sum('number');
                    $sum || $sum = 0;
                    $data['number'] = $sum;
                }
            }
            $result = $timeline->updateTimelineData($data);
            unset($timeline);
            unset($data);
        }
        DB::commit();
        $end = microtime(true);
        //echo '本次插入执行'. ($end - $start) . '秒';
    }

    /**
     * 更新当前时间线实例数据
     *
     * @param array $data 包含:start_price,end_price,highest,mminimum,number中任意键的数组
     * @return bool
     */
    public function updateTimelineData($data)
    {
        if (isset($this->day_time) && isset($this->type) && isset($this->currency_id) && isset($this->legal_id)) {
            isset($data['start_price']) && $this->start_price = $data['start_price'];
            isset($data['end_price']) && $this->end_price = $data['end_price'];
            isset($data['highest']) && $this->highest = $data['highest'];
            isset($data['mminimum']) && $this->mminimum = $data['mminimum'];
            isset($data['number']) && $this->number = $data['number'];
            isset($data['period']) || $this->period = self::$period[$this->type];
            $result = $this->save();
            return $result;
        } else {
            return false;
        }
    }

    /**
     * 设置时间线数据
     *
     * @param integer $type 类型:1.15分钟,2.1小时,3.4小时,4.一天,5.分时,6.5分钟，7.30分钟,8.一周,9.一月
     * @param integer $currency_id 币种id
     * @param integer $legal_id 法币id
     * @param array $data 包含:start_price,end_price,highest,mminimum,number中任意键的数组
     * @param integer $sign 来源标记[0.默认,1.交易更新,2.接口,3.后台添加
     * @param integer $day_time 时间戳
     * @return bool
     */
    public static function setTimelineData($type, $currency_id, $legal_id, $data, $sign = 0, $day_time = null)
    {
        empty($day_time) && $day_time = time();
        $timeline = self::getTimelineInstance($type, $currency_id, $legal_id, $sign, $day_time);
        if (empty($data) || !is_array($data)) {
            return false;
        }
        isset($data['start_price']) && $timeline->start_price = $data['start_price'];
        isset($data['end_price']) && $timeline->end_price = $data['end_price'];
        isset($data['highest']) && $timeline->highest = $data['highest'];
        isset($data['mminimum']) && $timeline->mminimum = $data['mminimum'];
        isset($data['number']) && $timeline->number = $data['number'];
        isset($data['period']) || $timeline->period = self::$period[$type];
        $result = $timeline->save();
        return $result;
    }

    /**
     * 取指定类型时间线实例,如果没有自动创建
     *
     * @param integer $type 类型:[1.15分钟,2.1小时,3.4小时,4.一天,5.分时,6.5分钟，7.30分钟,8.一周,9.一月]
     * @param integer $currency_id 币种id
     * @param integer $legal_id 法币id
     * @param integer $sign 来源标记[0.默认,1.交易更新,2.接口,3.后台添加
     * @param integer $day_time 时间戳
     * @return void
     */
    public static function getTimelineInstance($type, $currency_id, $legal_id, $sign = 0, $day_time = null)
    {
        empty($day_time) && $day_time = time();
        $time = self::formatTimeline($type, $day_time);
        $timeline = self::where('type', $type)
            ->where('day_time', $time)
            ->where('currency_id', $currency_id)
            ->where('legal_id', $legal_id)
            ->first();
        if (!$timeline) {
            $timeline = self::makeTimelineData($type, $currency_id, $legal_id, $sign, $day_time);
        } else {
            $timeline->sign = $sign;
        }
        return $timeline;
    }

    /**
     * 生成一条时间线数据
     *
     * @param integer $type 类型:1.15分钟,2.1小时,3.4小时,4.一天,5.分时,6.5分钟，7.30分钟,8.一周,9.一月
     * @param integer $currency_id 币种id
     * @param integer $legal_id 法币id
     * @param integer $sign 来源标记[0.默认,1.交易更新,2.接口,3.后台添加
     * @param integer $day_time 时间戳
     */
    private static function makeTimelineData($type, $currency_id, $legal_id, $sign = 0, $day_time = null)
    {
        empty($day_time) && $day_time = time();
        $time = self::formatTimeline($type, $day_time);
        $timeline = new self();
        $timeline->type = $type;
        $timeline->day_time = $time;
        $timeline->currency_id = $currency_id;
        $timeline->legal_id = $legal_id;
        $timeline->start_price = 0;
        $timeline->end_price = 0;
        $timeline->highest = 0;
        $timeline->mminimum = 0;
        $timeline->number = 0;
        $timeline->sign = $sign;
        $timeline->period = self::$period[$type];
        $result = $timeline->save();
        return $timeline;
    }

    /**
     * 按类型格式化时间线
     *
     * @param integer $type 类型:1.15分钟,2.1小时,3.一年,4.一天,5.分时,6.5分钟，7.30分钟,8.一周,9.一月,10.4小时
     * @param integer $day_time 时间戳,不传将默认采用当前时间
     */
    public static function formatTimeline($type, $day_time = null)
    {
        empty($day_time) && $day_time = time();
        switch ($type) {
            //15分钟
            case 1:
                $start_time = strtotime(date('Y-m-d H:00:00', $day_time));
                $minute = intval(date('i', $day_time));
                $multiple = floor($minute / 15);
                $minute = $multiple * 15;
                $time = $start_time + $minute * 60;
                break;
            //1小时
            case 2:
                $time = strtotime(date('Y-m-d H:00:00', $day_time));
                break;
            //4小时
            case 3:
                $start_time = strtotime(date('Y-m-d', $day_time));
                $hours = intval(date('H', $day_time));
                $multiple = floor($hours / 4);
                $hours = $multiple * 4;
                $time = $start_time + $hours * 3600;
                break;
            //一天
            case 4:
                $time = strtotime(date('Y-m-d', $day_time));
                break;
            //分时
            case 5:
                $time_string = date('Y-m-d H:i', $day_time);
                $time = strtotime($time_string);
                break;
            //5分钟
            case 6:
                $start_time = strtotime(date('Y-m-d H:00:00', $day_time));
                $minute = intval(date('i', $day_time));
                $multiple = floor($minute / 5);
                $minute = $multiple * 5;
                $time = $start_time + $minute * 60;
                break;
            //30分钟
            case 7:
                $start_time = strtotime(date('Y-m-d H:00:00', $day_time));
                $minute = intval(date('i', $day_time));
                $multiple = floor($minute / 30);
                $minute = $multiple * 30;
                $time = $start_time + $minute * 60;
                break;
            //一周
            case 8:
                $start_time = strtotime(date('Y-m-d', $day_time));
                $week = intval(date('w', $day_time));
                $diff_day = $week;
                $time = $start_time - $diff_day * 86400;
                break;
            //一月
            case 9:
                $time_string = date('Y-m', $day_time);
                $time = strtotime($time_string);
                break;
            //一年
            case 10:
                $time = strtotime(date('Y-01-01', $day_time));
                break;
            default:
                $time = $day_time;
                break;
        }
        return $time;
    }

    public static function getHuobiLeverMarket()
    {
        $currency_match = CurrencyMatch::where('market_from', 2)
            ->where('open_lever', 1)
            ->get();
        if (count($currency_match) <= 0) {
            return false;
        }
        return $currency_match;
    }
    public static function setEsearchMarket2($market_data)
    {
        $sy = DB::table('market_kine')->where('id',$market_data['id'])->where('symbol',$market_data['base-currency'])->where('period',$market_data['period'])->get();
        if(count($sy) > 0){
            $data['open'] = $market_data['open'];
            $data['close'] = $market_data['close'];
            $data['high'] = $market_data['high'];
            $data['low'] = $market_data['low'];
            $data['vol'] = $market_data['vol'];
            $data['amount'] = $market_data['amount'];
            DB::table('market_kine')->where('period',$market_data['period'])->where('id',$market_data['id'])->where('symbol',$market_data['base-currency'])->update($data);
        }else{
            $data['period'] = $market_data['period'];
            $data['symbol'] = $market_data['base-currency'];
            $data['open'] = $market_data['open'];
            $data['close'] = $market_data['close'];
            $data['high'] = $market_data['high'];
            $data['low'] = $market_data['low'];
            $data['vol'] = $market_data['vol'];
            $data['amount'] = $market_data['amount'];
            $data['time'] = $market_data['id'];
            $data['id'] = $market_data['id'];
            DB::table('market_kine')->insert($data);
        }
        return true;

    }

    public static function setEsearchMarket($market_data)
    {
        try {
            $es_client = self::getEsearchClient();

            // 文档ID（确保唯一性）
            $documentId = $market_data['base-currency'] . '_' .
                         ($market_data['quote-currency'] ?? 'USDT') . '_' .
                         $market_data['period'] . '_' .
                         $market_data['id'];

            // 使用ES的upsert功能：存在则更新，不存在则插入
            $upsertParams = [
                'index' => 'market.kline',
                'type' => '_doc',
                'id' => $documentId,
                'body' => [
                    // 如果文档存在，只更新这些字段（类似MySQL UPDATE）
                    'doc' => [
                        'close' => floatval($market_data['close']),
                        'high' => floatval($market_data['high']),
                        'low' => floatval($market_data['low']),
                        'vol' => floatval($market_data['vol']),
                        'amount' => floatval($market_data['amount']),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    // 如果文档不存在，插入完整文档（类似MySQL INSERT）
                    'upsert' => [
                        'id' => intval($market_data['id']),  // 确保是整数
                        'period' => $market_data['period'],
                        'base-currency' => $market_data['base-currency'],
                        'quote-currency' => $market_data['quote-currency'] ?? 'USDT',
                        'open' => floatval($market_data['open']),
                        'close' => floatval($market_data['close']),
                        'high' => floatval($market_data['high']),
                        'low' => floatval($market_data['low']),
                        'vol' => floatval($market_data['vol']),
                        'amount' => floatval($market_data['amount']),
                        'timestamp' => intval($market_data['id']),  // 确保是整数
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                ]
            ];

            $response = $es_client->update($upsertParams);

            \Log::info('Market data upserted to ES successfully', [
                'symbol' => $market_data['base-currency'],
                'period' => $market_data['period'],
                'id' => $market_data['id'],
                'operation' => $response['result'] ?? 'unknown', // 'created' 或 'updated'
                'es_response' => $response
            ]);

        } catch (\Exception $e) {
            \Log::error("ES upsert failed", [
                'symbol' => $market_data['base-currency'],
                'period' => $market_data['period'],
                'id' => $market_data['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // ES操作失败时，作为备用方案仍然写入MySQL（可选）
            // 如果你想完全避免MySQL死锁，可以注释掉下面的代码
            /*
            try {
                $sy = DB::table('market_kine')->where('id',$market_data['id'])->where('symbol',$market_data['base-currency'])->where('period',$market_data['period'])->get();
                if(count($sy) > 0){
                    $data['close'] = $market_data['close'];
                    $data['high'] = $market_data['high'];
                    $data['low'] = $market_data['low'];
                    $data['vol'] = $market_data['vol'];
                    $data['amount'] = $market_data['amount'];
                    DB::table('market_kine')->where('period',$market_data['period'])->where('id',$market_data['id'])->where('symbol',$market_data['base-currency'])->update($data);
                }else{
                    $data['period'] = $market_data['period'];
                    $data['symbol'] = $market_data['base-currency'];
                    $data['open'] = $market_data['open'];
                    $data['close'] = $market_data['close'];
                    $data['high'] = $market_data['high'];
                    $data['low'] = $market_data['low'];
                    $data['vol'] = $market_data['vol'];
                    $data['amount'] = $market_data['amount'];
                    $data['time'] = $market_data['id'];
                    $data['id'] = $market_data['id'];
                    DB::table('market_kine')->insert($data);
                }
            } catch (\Exception $mysqlException) {
                \Log::error("Both ES and MySQL operation failed", [
                    'symbol' => $market_data['base-currency'],
                    'es_error' => $e->getMessage(),
                    'mysql_error' => $mysqlException->getMessage()
                ]);
            }
            */
        }
    }

    public static function getAndSetEsearchMarket($market_data)
    {
        // 直接使用 upsert，让 ES 内部处理存在性检查，避免额外的查询
        // 使用 script 来处理 high/low 的比较逻辑
        try {
            $es_client = self::getEsearchClient();

            // 文档ID（确保唯一性）
            $documentId = $market_data['base-currency'] . '_' .
                         ($market_data['quote-currency'] ?? 'USDT') . '_' .
                         $market_data['period'] . '_' .
                         $market_data['id'];

            // 使用 script 进行智能更新，避免额外查询
            $scriptParams = [
                'index' => 'market.kline',
                'type' => '_doc',
                'id' => $documentId,
                'body' => [
                    'script' => [
                        'source' => '
                            if (ctx._source == null) {
                                ctx._source = params.doc;
                            } else {
                                ctx._source.close = params.doc.close;
                                ctx._source.vol = params.doc.vol;
                                ctx._source.amount = params.doc.amount;
                                ctx._source.updated_at = params.doc.updated_at;

                                // 只有新的 high 更高时才更新
                                if (params.doc.high > ctx._source.high) {
                                    ctx._source.high = params.doc.high;
                                }

                                // 只有新的 low 更低时才更新
                                if (params.doc.low < ctx._source.low) {
                                    ctx._source.low = params.doc.low;
                                }
                            }
                        ',
                        'params' => [
                            'doc' => [
                                'id' => intval($market_data['id']),
                                'period' => $market_data['period'],
                                'base-currency' => $market_data['base-currency'],
                                'quote-currency' => $market_data['quote-currency'] ?? 'USDT',
                                'open' => floatval($market_data['open']),
                                'close' => floatval($market_data['close']),
                                'high' => floatval($market_data['high']),
                                'low' => floatval($market_data['low']),
                                'vol' => floatval($market_data['vol']),
                                'amount' => floatval($market_data['amount']),
                                'timestamp' => intval($market_data['id']),
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s')
                            ]
                        ]
                    ],
                    'upsert' => [
                        'id' => intval($market_data['id']),
                        'period' => $market_data['period'],
                        'base-currency' => $market_data['base-currency'],
                        'quote-currency' => $market_data['quote-currency'] ?? 'USDT',
                        'open' => floatval($market_data['open']),
                        'close' => floatval($market_data['close']),
                        'high' => floatval($market_data['high']),
                        'low' => floatval($market_data['low']),
                        'vol' => floatval($market_data['vol']),
                        'amount' => floatval($market_data['amount']),
                        'timestamp' => intval($market_data['id']),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                ]
            ];

            $response = $es_client->update($scriptParams);

            return true;

        } catch (\Exception $e) {
            \Log::error("ES script upsert failed", [
                'symbol' => $market_data['base-currency'],
                'period' => $market_data['period'],
                'id' => $market_data['id'],
                'error' => $e->getMessage()
            ]);

            // 降级到原来的方法
            $result = self::getEsearchMarketById($market_data['base-currency'], $market_data['quote-currency'], $market_data['period'], $market_data['id']);
            if (isset($result['_source'])) {
                $data = $result['_source'];
                bc_comp($market_data['high'], $data['high']) < 0 && $market_data['high'] = $data['high'];
                bc_comp($market_data['low'], $data['low']) > 0 && $market_data['low'] = $data['low'];
            }
            return self::setEsearchMarket($market_data);
        }
    }

    public static function getEsearchMarketById($base_currency, $quote_currency, $peroid, $id)
    {
        // 检查 ES 健康状态，如果不健康直接返回空数据
        if (!\App\Services\ElasticsearchHealthService::isHealthy()) {
            return [];
        }

        try {
            $es_client = self::getEsearchClient();

            // 构建文档ID
            $documentId = $base_currency . '_' .
                         ($quote_currency ?? 'USDT') . '_' .
                         $peroid . '_' .
                         $id;

            // ES查询参数
            $params = [
                'index' => 'market.kline',
                'type' => '_doc',
                'id' => $documentId
            ];

            // 执行ES查询
            $response = $es_client->get($params);

            if (isset($response['_source'])) {
                return [
                    '_source' => $response['_source']
                ];
            } else {
                return [];
            }

        } catch (\Exception $e) {
            // 减少日志频率，避免日志文件过大
            if (rand(1, 100) <= 1) { // 只记录1%的ES失败日志
                \Log::warning("ES read failed, returning empty data", [
                    'symbol' => $base_currency,
                    'period' => $peroid,
                    'id' => $id,
                    'error' => $e->getMessage()
                ]);
            }

            // ES查询失败时，直接返回空数据，不再查询MySQL
            return [];
        }
    }



    /**
     * 从ElasticSearch取行情
     *
     * @param string $base_currency 基础币种，即交易币
     * @param string $quote_currency 计价币种，即法币
     * @param string $peroid 行情时间分辨率
     * @param integer $from 开始时间戳
     * @param integer $to 结束时间戳
     * @return array
     */
    public static function getEsearchMarket($base_currency, $quote_currency, $peroid, $from, $to)
    {
        try {
            $es_client = self::getEsearchClient();

            // 计算查询大小
            $size = intval(($to - $from) / 60) + 100; // 假设最小间隔1分钟
            $size = min($size, 10000); // 限制最大查询数量

            // ES查询参数
            $params = [
                'index' => 'market.kline',
                'type' => '_doc',
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['match' => ['period' => $peroid]],
                                ['match' => ['base-currency' => $base_currency]],
                                ['match' => ['quote-currency' => $quote_currency ?? 'USDT']],
                            ],
                            'filter' => [
                                'range' => [
                                    'id' => [
                                        'gte' => $from,
                                        'lte' => $to
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'sort' => [
                        [
                            'id' => [
                                'order' => 'asc'
                            ]
                        ],
                        [
                            'timestamp' => [
                                'order' => 'asc'
                            ]
                        ]
                    ],
                    'size' => $size
                ]
            ];

            // 添加ES查询调试日志
            \Log::info('ES查询参数', [
                'base_currency' => $base_currency,
                'quote_currency' => $quote_currency,
                'period' => $peroid,
                'from' => $from,
                'to' => $to,
                'query_params' => $params
            ]);

            // 执行ES查询
            $response = $es_client->search($params);

            // 添加ES查询结果日志
            \Log::info('ES查询结果', [
                'total_hits' => $response['hits']['total']['value'] ?? 0,
                'returned_hits' => count($response['hits']['hits'] ?? [])
            ]);

            if (isset($response['hits']['hits'])) {
                $data = array_column($response['hits']['hits'], '_source');

                // 转换为与MySQL查询结果兼容的格式
                $result = collect($data)->map(function ($item) {
                    return (object) [
                        'id' => $item['id'],
                        'symbol' => $item['base-currency'],
                        'period' => $item['period'],
                        'open' => $item['open'],
                        'close' => $item['close'],
                        'high' => $item['high'],
                        'low' => $item['low'],
                        'vol' => $item['vol'],
                        'amount' => $item['amount'],
                        'time' => $item['timestamp'] ?? $item['id']
                    ];
                });

                return $result;
            } else {
                return collect([]);
            }

        } catch (\Exception $e) {
            // 减少日志频率
            if (rand(1, 100) <= 1) { // 只记录1%的ES失败日志
                \Log::warning("ES range query failed, returning empty data", [
                    'symbol' => $base_currency,
                    'period' => $peroid,
                    'from' => $from,
                    'to' => $to,
                    'error' => $e->getMessage()
                ]);
            }

            // ES查询失败时，直接返回空数据，不再查询MySQL
            return collect([]);
        }
    }



    public static function batchEsearchMarket($base_currency, $quote_currency, $price, $time)
    {

        $currency = Currency::where('name', $base_currency)->first();
        $legal = Currency::where('name', $quote_currency)->first();
        $currency_match = CurrencyMatch::where('currency_id', $currency->id)
            ->where('legal_id', $legal->id)
            ->first();
        $types = [
            '1min'=> 5,
            '5min'=> 6,
            '15min'=> 1,
            '30min'=> 7,
            '60min'=> 2,
            '1day'=> 4,
            '1mon'=> 9,
            '1week'=> 8,
            '1year'=> 10,
        ];
        $periods = ['1min', '5min', '15min', '30min', '60min', '1day', '1mon', '1week', '1year'];
        foreach ($periods as $key => $period) {
            $type = $types[$period];
            $convert_time = self::formatTimeline($type, $time);
            $result = self::getEsearchMarketById($base_currency, $quote_currency, $period, $convert_time);
            if (isset($result['_source'])) {
                $data = $result['_source'];
                $data['close'] = $price; //更新下最新价格
                bc_comp($data['high'], $price) < 0 && $data['high'] = $price; //更新最高价
                bc_comp($data['low'], $price) > 0 && $data['low'] = $price; //更新最低价

                // unset($data['vol'], $data['amount']); //不影响成交数量

                self::setEsearchMarket($data);
            } else {
                //dd($result);
                //拿不到数据,可能是还没有也有可能是程序错误,为了保险建议不处理
                $data = [
                    'id' => $convert_time,
                    'period' => $period,
                    'base-currency' => $base_currency,
                    'quote-currency' => $quote_currency,
                    'open' => $price,
                    'close' => $price,
                    'high' => $price,
                    'low' => $price,
                    'vol' => 0,
                    'amount' => 0,
                ];
                //self::setEsearchMarket($data);
            }
            //dump($data);
            if ($period == '1min') {
                $kline_data = [
                    'type' => 'kline',
                    'period' => '1min',
                    'match_id' => $currency_match->id,
                    'currency_id' => $currency->id,
                    'currency_name' => $base_currency,
                    'legal_id' => $legal->id,
                    'legal_name' => $quote_currency,
                    'open' => $data['open'],
                    'close' => $data['close'],
                    'high' => $data['high'],
                    'low' => $data['low'],
                    'symbol' => $currency_match->currency_name . '/' . $currency_match->legal_name,
                    'volume' => $data['amount'] ?? 0,
                    'time' => $convert_time * 1000,
                    'market_form' => 'risk',
                ];

            }
            //  var_dump(['data' => $kline_data,'d' => '123']);
            SendMarket::dispatch($kline_data)->onQueue('kline.1min');
            // self::setEsearchMarket($data);
        }
    }

    public static function getLastEsearchMarket($base_currency, $quote_currency, $peroid = '1min')
    {
        $es_client = self::getEsearchClient();
        $params = [
            'index' => 'market.kline',
            'type' =>  '_doc',
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            ['match' => ['period' => $peroid]],
                            ['match' => ['base-currency' => $base_currency]],
                            ['match' => ['quote-currency' => $quote_currency]],
                        ],
                    ],
                ],
                'sort' => [
                    'id' => [
                        'order' => 'desc',
                    ],
                ],
                'size' => 1,
            ],
        ];
        $result = $es_client->search($params);
        if (isset($result['hits'])) {
            $data = array_column($result['hits']['hits'], '_source');
            $data = reset($data);
        } else {
            $data = [];
        }
        return $data;
    }
}
