<?php

namespace App\Console\Commands;

use App\Models\MarketKine;
use Illuminate\Console\Command;

class ClearHour extends Command
{
    protected $signature = "ClearHour";
    protected $description = "清理1小时、半小时数据";
    public function handle()
    {
        $utcTime = gmdate('Y-m-d H:i:s');
// 如果你想检查这个UTC时间是否是周末，你可以这样做：
        $utcTimestamp = strtotime($utcTime); // 通常这一步是多余的，因为gmdate()已经返回了UTC时间
        $utcDayOfWeek = date('N', $utcTimestamp); // 但这里我们使用date()和UTC时间戳来检查星期几
        if ($utcDayOfWeek == 6 || $utcDayOfWeek == 7) {
            // echo "今天是周六或周日。";
            return;
        }
        MarketKine::where('period','30min')->delete();
        MarketKine::where('period','60min')->delete();
    }
}

