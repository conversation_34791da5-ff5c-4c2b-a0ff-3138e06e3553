[2025-09-09 05:53:54] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411580,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411580\",\"found\":false}"} 
[2025-09-09 05:53:54] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757411580,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757411580\",\"found\":false}"} 
[2025-09-09 05:54:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:54:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757411640,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757411640\",\"found\":false}"} 
[2025-09-09 05:55:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:11] local.INFO: Exchange Rate Updated {"agreement":"BTC","old_rate":112907.68,"new_rate":112901.01,"updated_at":"2025-09-09 05:55:11"} 
[2025-09-09 05:55:11] local.INFO: Digital Currency Address Price Updated {"agreement":"BTC","usd_price":112901.01,"affected_rows":1,"updated_at":"2025-09-09 05:55:11"} 
[2025-09-09 05:55:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:11] local.INFO: Exchange Rate Updated {"agreement":"ETH","old_rate":4356.67,"new_rate":4355.29,"updated_at":"2025-09-09 05:55:11"} 
[2025-09-09 05:55:11] local.INFO: Digital Currency Address Price Updated {"agreement":"ETH","usd_price":4355.29,"affected_rows":1,"updated_at":"2025-09-09 05:55:11"} 
[2025-09-09 05:55:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:55:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757411700,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757411700\",\"found\":false}"} 
[2025-09-09 05:56:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:56:41] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757411760,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757411760\",\"found\":false}"} 
[2025-09-09 05:57:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:57:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757411820,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757411820\",\"found\":false}"} 
[2025-09-09 05:58:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:58:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757411880,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757411880\",\"found\":false}"} 
[2025-09-09 05:59:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 05:59:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757411940,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757411940\",\"found\":false}"} 
[2025-09-09 06:00:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:17] local.INFO: Exchange Rate Updated {"agreement":"BTC","old_rate":112901.01,"new_rate":112955.33,"updated_at":"2025-09-09 06:00:17"} 
[2025-09-09 06:00:17] local.INFO: Digital Currency Address Price Updated {"agreement":"BTC","usd_price":112955.33,"affected_rows":1,"updated_at":"2025-09-09 06:00:17"} 
[2025-09-09 06:00:17] local.INFO: Exchange Rate Updated {"agreement":"ETH","old_rate":4355.29,"new_rate":4356.5,"updated_at":"2025-09-09 06:00:17"} 
[2025-09-09 06:00:17] local.INFO: Digital Currency Address Price Updated {"agreement":"ETH","usd_price":4356.5,"affected_rows":1,"updated_at":"2025-09-09 06:00:17"} 
[2025-09-09 06:00:25] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:25] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:25] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:00:25] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:46] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:01:46] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"15min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_15min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:46] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"30min","id":1757412000.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_30min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:46] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"60min","id":1757412000,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_60min_1757412000\",\"found\":false}"} 
[2025-09-09 06:01:46] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412060,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412060\",\"found\":false}"} 
[2025-09-09 06:02:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:26] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:26] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:02:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:03:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:03:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:03:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412120,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412120\",\"found\":false}"} 
[2025-09-09 06:03:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:31] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:31] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:03:31] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412180,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412180\",\"found\":false}"} 
[2025-09-09 06:04:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:29] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:29] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:04:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412240,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412240\",\"found\":false}"} 
[2025-09-09 06:05:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:05:14] local.INFO: Exchange Rate Updated {"agreement":"BTC","old_rate":112955.33,"new_rate":112932.46,"updated_at":"2025-09-09 06:05:14"} 
[2025-09-09 06:05:14] local.INFO: Digital Currency Address Price Updated {"agreement":"BTC","usd_price":112932.46,"affected_rows":1,"updated_at":"2025-09-09 06:05:14"} 
[2025-09-09 06:05:14] local.INFO: Exchange Rate Updated {"agreement":"ETH","old_rate":4356.5,"new_rate":4358.36,"updated_at":"2025-09-09 06:05:14"} 
[2025-09-09 06:05:14] local.INFO: Digital Currency Address Price Updated {"agreement":"ETH","usd_price":4358.36,"affected_rows":1,"updated_at":"2025-09-09 06:05:14"} 
[2025-09-09 06:05:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412300,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412300\",\"found\":false}"} 
[2025-09-09 06:06:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:19] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:22] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:22] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:44] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:06:44] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412360,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412360\",\"found\":false}"} 
[2025-09-09 06:07:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:07:33] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412420,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412420\",\"found\":false}"} 
[2025-09-09 06:08:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:08:19] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412480,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412480\",\"found\":false}"} 
[2025-09-09 06:09:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:09:32] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412540,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412540\",\"found\":false}"} 
[2025-09-09 06:10:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:14] local.INFO: Exchange Rate Updated {"agreement":"BTC","old_rate":112932.46,"new_rate":112995.41,"updated_at":"2025-09-09 06:10:14"} 
[2025-09-09 06:10:14] local.INFO: Digital Currency Address Price Updated {"agreement":"BTC","usd_price":112995.41,"affected_rows":1,"updated_at":"2025-09-09 06:10:14"} 
[2025-09-09 06:10:14] local.INFO: Exchange Rate Updated {"agreement":"ETH","old_rate":4358.36,"new_rate":4359.4,"updated_at":"2025-09-09 06:10:14"} 
[2025-09-09 06:10:14] local.INFO: Digital Currency Address Price Updated {"agreement":"ETH","usd_price":4359.4,"affected_rows":1,"updated_at":"2025-09-09 06:10:14"} 
[2025-09-09 06:10:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:23] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:10:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:11:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:11:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:11:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412600,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412600\",\"found\":false}"} 
[2025-09-09 06:11:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:14] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:11:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412660,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412660\",\"found\":false}"} 
[2025-09-09 06:12:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:12:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:13:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412720,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412720\",\"found\":false}"} 
[2025-09-09 06:13:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:13:23] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412780,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412780\",\"found\":false}"} 
[2025-09-09 06:14:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:19] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:19] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:19] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:23] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:24] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:24] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:14:53] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412840,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412840\",\"found\":false}"} 
[2025-09-09 06:15:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:14] local.INFO: Exchange Rate Updated {"agreement":"BTC","old_rate":112995.41,"new_rate":112970.33,"updated_at":"2025-09-09 06:15:14"} 
[2025-09-09 06:15:14] local.INFO: Digital Currency Address Price Updated {"agreement":"BTC","usd_price":112970.33,"affected_rows":1,"updated_at":"2025-09-09 06:15:14"} 
[2025-09-09 06:15:14] local.INFO: Exchange Rate Updated {"agreement":"ETH","old_rate":4359.4,"new_rate":4357.52,"updated_at":"2025-09-09 06:15:14"} 
[2025-09-09 06:15:14] local.INFO: Digital Currency Address Price Updated {"agreement":"ETH","usd_price":4357.52,"affected_rows":1,"updated_at":"2025-09-09 06:15:14"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:17] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:20] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:33] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412900,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412900\",\"found\":false}"} 
[2025-09-09 06:15:33] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"15min","id":1757412900.0,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_15min_1757412900\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:36] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:36] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:16:48] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757412960,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757412960\",\"found\":false}"} 
[2025-09-09 06:17:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:36] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:38] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:39] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:39] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:44] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:17:59] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413020,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413020\",\"found\":false}"} 
[2025-09-09 06:18:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:18:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413080,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413080\",\"found\":false}"} 
[2025-09-09 06:19:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:19:11] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413140,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413140\",\"found\":false}"} 
[2025-09-09 06:20:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:14] local.INFO: Exchange Rate Updated {"agreement":"BTC","old_rate":112970.33,"new_rate":112954.33,"updated_at":"2025-09-09 06:20:14"} 
[2025-09-09 06:20:14] local.INFO: Digital Currency Address Price Updated {"agreement":"BTC","usd_price":112954.33,"affected_rows":1,"updated_at":"2025-09-09 06:20:14"} 
[2025-09-09 06:20:14] local.INFO: Exchange Rate Updated {"agreement":"ETH","old_rate":4357.52,"new_rate":4359.84,"updated_at":"2025-09-09 06:20:14"} 
[2025-09-09 06:20:14] local.INFO: Digital Currency Address Price Updated {"agreement":"ETH","usd_price":4359.84,"affected_rows":1,"updated_at":"2025-09-09 06:20:14"} 
[2025-09-09 06:20:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:20:45] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413200,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413200\",\"found\":false}"} 
[2025-09-09 06:21:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:10] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:12] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:21:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413260,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413260\",\"found\":false}"} 
[2025-09-09 06:22:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:13] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:24] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:25] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:25] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:30] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:41] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:41] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:43] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:22:44] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413320,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413320\",\"found\":false}"} 
[2025-09-09 06:23:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:09] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:36] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:23:36] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:24:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:24:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413380,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413380\",\"found\":false}"} 
[2025-09-09 06:24:00] local.WARNING: ES read failed, falling back to MySQL {"symbol":"BTCUSDT","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"BTCUSDT_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURJPY","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURJPY_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:01] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USDCAD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USDCAD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPAUD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPAUD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPJPY","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPJPY_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPNZD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPNZD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:02] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDJPY","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDJPY_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAUUSD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAUUSD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:03] local.WARNING: ES read failed, falling back to MySQL {"symbol":"XAGUSD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"XAGUSD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ETHUSDT","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ETHUSDT_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"LTCUSDT","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"LTCUSDT_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDJPY","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDJPY_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"AUDNZD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"AUDNZD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURAUD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURAUD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:04] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCHF","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCHF_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UKOIL","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UKOIL_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:05] local.WARNING: ES read failed, falling back to MySQL {"symbol":"USOIL","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"USOIL_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NGAS","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NGAS_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GER30","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GER30_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"US500","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"US500_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURNZD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURNZD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:06] local.WARNING: ES read failed, falling back to MySQL {"symbol":"UK100","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"UK100_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:07] local.WARNING: ES read failed, falling back to MySQL {"symbol":"SG20","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"SG20_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:08] local.WARNING: ES read failed, falling back to MySQL {"symbol":"ND25","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"ND25_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:15] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURCAD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURCAD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:16] local.WARNING: ES read failed, falling back to MySQL {"symbol":"GBPUSD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"GBPUSD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"EURUSD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"EURUSD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"NZDUSD","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"NZDUSD_USDT_1min_1757413440\",\"found\":false}"} 
[2025-09-09 06:24:18] local.WARNING: ES read failed, falling back to MySQL {"symbol":"CADJPY","period":"1min","id":1757413440,"error":"{\"_index\":\"market.kline\",\"_type\":\"_doc\",\"_id\":\"CADJPY_USDT_1min_1757413440\",\"found\":false}"} 
