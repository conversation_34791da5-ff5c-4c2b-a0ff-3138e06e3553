<?php

namespace App\Nova\Extract;

use App\Nova\Actions\ExtractPass;
use App\Nova\Actions\ExtractRefuse;
use App\Nova\Resource;
use <PERSON>vel\Nova\Fields\Badge;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class UsersWalletOut extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\UsersWalletOut>
     */
    public static $model = \App\Models\UsersWalletOut::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static function group(){
        return __('Wallet');
    }

    /**
     * Custom priority level of the resource.
     *
     * @var int
     */
    public static $priority = 3;


    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'user.account_number',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make(__('AccountNumber'), 'account')->readonly(),
            Text::make(__('CurrencyName'), 'currency')->readonly(),

            Text::make(__('Extract Address'), 'address')->readonly(),
//            Text::make(__('BankName'), 'bank_title')->readonly(),
//            Text::make(__('BankAddress'), 'bank_dizhi')->readonly(),
//            Text::make(__('Payee'), 'payee')->readonly(),
//            Text::make(__('Swift'), 'swift')->readonly(),
            Number::make(__('Extract Number'), 'number')->readonly(),
            Number::make(__('ServiceCharge'), 'rate')->readonly(),
            Number::make(__('ReceivedNumber'), 'real_number')->readonly(),
            Badge::make(__('Status'), 'status_name')->map([//danger
                'Apply for currency withdrawal' => 'info',
                '成功' => 'success',
                'fail' => 'danger'
            ]),
            Text::make(__('Time Of Application'), 'create_time')->readonly()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            ( new ExtractPass())->showInline(),
            (new ExtractRefuse())->showInline()
        ];
    }

    /**
     * Get the displayble label of the resource.
     *
     * @return string
     */
    public static function label()
    {
        return __('UsersWalletOut');
    }

    /**
     * Get the displayble singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('UsersWalletOut');
    }
}
