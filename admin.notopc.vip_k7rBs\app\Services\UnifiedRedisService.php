<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class UnifiedRedisService
{
    /**
     * 获取Redis实例 - 使用我们的连接池
     */
    public static function getRedis($database = 1)
    {
        return RedisService::getInstance($database);
    }

    /**
     * 替换Laravel Cache的get方法
     */
    public static function cacheGet($key, $database = 1)
    {
        $redis = self::getRedis($database);
        $value = $redis->get("cache:{$key}");
        
        if ($value === null || $value === false) {
            return null;
        }
        
        // 尝试反序列化
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }
        
        return $value;
    }

    /**
     * 替换Laravel Cache的put方法
     */
    public static function cachePut($key, $value, $ttl = 3600, $database = 1)
    {
        $redis = self::getRedis($database);
        
        // 序列化值
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value);
        }
        
        if ($ttl > 0) {
            return $redis->setex("cache:{$key}", $ttl, $value);
        } else {
            return $redis->set("cache:{$key}", $value);
        }
    }

    /**
     * 替换Laravel Cache的has方法
     */
    public static function cacheHas($key, $database = 1)
    {
        $redis = self::getRedis($database);
        return $redis->exists("cache:{$key}") > 0;
    }

    /**
     * 替换Laravel Cache的forget方法
     */
    public static function cacheForget($key, $database = 1)
    {
        $redis = self::getRedis($database);
        return $redis->del("cache:{$key}") > 0;
    }

    /**
     * 替换Laravel Cache的remember方法
     */
    public static function cacheRemember($key, $ttl, $callback, $database = 1)
    {
        $value = self::cacheGet($key, $database);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        self::cachePut($key, $value, $ttl, $database);
        
        return $value;
    }

    /**
     * Redis基本操作 - set
     */
    public static function set($key, $value, $ttl = null, $database = 1)
    {
        $redis = self::getRedis($database);
        
        if ($ttl) {
            return $redis->setex($key, $ttl, $value);
        } else {
            return $redis->set($key, $value);
        }
    }

    /**
     * Redis基本操作 - get
     */
    public static function get($key, $database = 1)
    {
        $redis = self::getRedis($database);
        return $redis->get($key);
    }

    /**
     * Redis基本操作 - del
     */
    public static function del($key, $database = 1)
    {
        $redis = self::getRedis($database);
        return $redis->del($key);
    }

    /**
     * Redis基本操作 - exists
     */
    public static function exists($key, $database = 1)
    {
        $redis = self::getRedis($database);
        return $redis->exists($key) > 0;
    }

    /**
     * Redis基本操作 - setex (带过期时间)
     */
    public static function setex($key, $ttl, $value, $database = 1)
    {
        $redis = self::getRedis($database);
        return $redis->setex($key, $ttl, $value);
    }

    /**
     * 获取Redis分布式锁
     */
    public static function getLock($lockKey, $timeout = 10, $database = 1)
    {
        $redis = self::getRedis($database);
        $identifier = uniqid();
        
        $result = $redis->set("lock:{$lockKey}", $identifier, 'EX', $timeout, 'NX');
        
        if ($result) {
            return $identifier;
        }
        
        return false;
    }

    /**
     * 释放Redis分布式锁
     */
    public static function releaseLock($lockKey, $identifier, $database = 1)
    {
        $redis = self::getRedis($database);
        
        $script = "
            if redis.call('GET', KEYS[1]) == ARGV[1] then
                return redis.call('DEL', KEYS[1])
            else
                return 0
            end
        ";
        
        return $redis->eval($script, 1, "lock:{$lockKey}", $identifier);
    }

    /**
     * 批量操作 - pipeline
     */
    public static function pipeline($callback, $database = 1)
    {
        $redis = self::getRedis($database);
        
        $redis->multi();
        $callback($redis);
        return $redis->exec();
    }

    /**
     * 获取连接池统计信息
     */
    public static function getPoolStats()
    {
        return RedisService::getPoolStatus();
    }

    /**
     * 兼容方法：获取Laravel Redis连接
     * 逐步替换 Redis::connection() 的使用
     */
    public static function connection($database = 1)
    {
        return self::getRedis($database);
    }
} 