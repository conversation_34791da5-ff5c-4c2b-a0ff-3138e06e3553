<?php

namespace App\Nova\Actions;

use App\Models\AccountLog;
use App\Models\Setting;
use App\Models\UsersWallet;
use App\Models\Users;
use App\Models\UserLevelModel;
use Illuminate\Bus\Queueable;
use Illuminate\Cache\RedisLock;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;
use App\Models\ChargeReq;
use PHPMailer\PHPMailer\PHPMailer;


class ApplyFilling extends Action
{
    use InteractsWithQueue, Queueable;

    public $name='同意';

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {

        foreach ($models as $model) {
            $user = Users::getById($model->uid);
            if($model->status<>1){
                return Action::danger('无法操作');
            }

            $legal = UsersWallet::where("user_id", $model->user->id)
                ->where("currency", 1)
                ->lockForUpdate()
                ->first();
            if(!$legal){
                return Action::danger('找不到用户钱包');
            }
            $redis = Redis::connection();
            $lock = new RedisLock($redis,'manual_charge'.$model->id,10);
            DB::beginTransaction();
            try{

                DB::table('charge_req')->where('id',$model->id)->update(['status'=>2,'updated_at'=>date('Y-m-d H:i:s')]);
                change_wallet_balance(
                    $legal,
                    2,
                    $model->amount,
                    AccountLog::WALLET_CURRENCY_IN,
                    'Deposit',
                    false,
                    0,
                    0,
                    serialize([
                    ]),
                    false,
                    true
                );
                // 计算用户升级
                UserLevelModel::checkUpgrade($model);
                $lock->release();
                DB::commit();
                //发送邮件
                //  从设置中取出值
                $username = Setting::getValueByKey('phpMailer_username', '');
                $host = Setting::getValueByKey('phpMailer_host', '');
                $password = Setting::getValueByKey('phpMailer_password', '');
                $port = Setting::getValueByKey('phpMailer_port', 465);
                $mail_from_name = Setting::getValueByKey('submail_from_name', '');
                $mail = new PHPMailer(true);
                $mail->isSMTP();
                $mail->CharSet = "utf-8";
                $mail->SMTPAuth = true;
                $mail->SMTPSecure = "ssl";
                $mail->Host = $host;
                $mail->Port = 465;//$port;
                $mail->Username = $username;
                $mail->Password = $password;//去开通的qq或163邮箱中找,这里用的不是邮箱的密码，而是开通之后的一个token
//            $mail->SMTPDebug = 2; //用于debug PHPMailer信息
                $mail->setFrom($username, $mail_from_name);//设置邮件来源  //发件人
                $mail->Subject = "Deposit coins successful"; //邮件标题
                $mail->MsgHTML("Your charging has been completed, you can proceed with the operation.");
                $mail->addAddress($user->email);  //收件人（用户输入的邮箱）
                $mail->send();
            }catch (\Exception $e){
                DB::rollBack();
                return Action::danger($e->getMessage());
            }
        }
        return Action::message('操作成功');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }



}
