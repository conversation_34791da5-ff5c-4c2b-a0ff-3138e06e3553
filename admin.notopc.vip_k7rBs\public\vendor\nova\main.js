(self.webpackChunklaravel_nova=self.webpackChunklaravel_nova||[]).push([[179],{2897:(a,r,l)=>{function e(a,r){var l=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);r&&(e=e.filter((function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable}))),l.push.apply(l,e)}return l}function o(a){for(var r=1;r<arguments.length;r++){var l=null!=arguments[r]?arguments[r]:{};r%2?e(Object(l),!0).forEach((function(r){t(a,r,l[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(l)):e(Object(l)).forEach((function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(l,r))}))}return a}function t(a,r,l){return r in a?Object.defineProperty(a,r,{value:l,enumerable:!0,configurable:!0,writable:!0}):a[r]=l,a}const c=l(7557),v=l(9808),u=a=>{let r=a.replace("#","");3===r.length&&(r=`${r[0]}${r[0]}${r[1]}${r[1]}${r[2]}${r[2]}`);return`${parseInt(r.substring(0,2),16)}, ${parseInt(r.substring(2,4),16)}, ${parseInt(r.substring(4,6),16)}`},s=c(o(o({primary:v.sky},v),{},{gray:v.slate}),["lightBlue","warmGray","trueGray","coolGray","blueGray"]);a.exports={generateRootCSSVars:function(){return Object.fromEntries(Object.entries(s).map((([a,r])=>"string"==typeof r?[[`--colors-${a}`,r]]:Object.entries(r).map((([r,l])=>[`--colors-${a}-${r}`,u(l)])))).flat(1))},generateTailwindColors:function(){return Object.fromEntries(Object.entries(s).map((([a,r])=>"string"==typeof r?[`${a}`,r]:[a,Object.fromEntries(Object.entries(r).map((([r,l])=>[`${r}`,`rgba(var(--colors-${a}-${r}), <alpha-value>)`])))])))}}},3098:(a,r,l)=>{"use strict";var e=l(2897);it("generates Tailwind colors",(()=>{expect((0,e.generateTailwindColors)()).toEqual(expect.objectContaining({current:"currentColor",inherit:"inherit",transparent:"transparent",black:"#000",white:"#fff",primary:{100:"rgba(var(--colors-primary-100), <alpha-value>)",200:"rgba(var(--colors-primary-200), <alpha-value>)",300:"rgba(var(--colors-primary-300), <alpha-value>)",400:"rgba(var(--colors-primary-400), <alpha-value>)",50:"rgba(var(--colors-primary-50), <alpha-value>)",500:"rgba(var(--colors-primary-500), <alpha-value>)",600:"rgba(var(--colors-primary-600), <alpha-value>)",700:"rgba(var(--colors-primary-700), <alpha-value>)",800:"rgba(var(--colors-primary-800), <alpha-value>)",900:"rgba(var(--colors-primary-900), <alpha-value>)"}}))}));const o={lightBlue:{100:"rgba(var(--colors-lightBlue-100), <alpha-value>)",200:"rgba(var(--colors-lightBlue-200), <alpha-value>)",300:"rgba(var(--colors-lightBlue-300), <alpha-value>)",400:"rgba(var(--colors-lightBlue-400), <alpha-value>)",50:"rgba(var(--colors-lightBlue-50), <alpha-value>)",500:"rgba(var(--colors-lightBlue-500), <alpha-value>)",600:"rgba(var(--colors-lightBlue-600), <alpha-value>)",700:"rgba(var(--colors-lightBlue-700), <alpha-value>)",800:"rgba(var(--colors-lightBlue-800), <alpha-value>)",900:"rgba(var(--colors-lightBlue-900), <alpha-value>)"},warmGray:{100:"rgba(var(--colors-warmGray-100), <alpha-value>)",200:"rgba(var(--colors-warmGray-200), <alpha-value>)",300:"rgba(var(--colors-warmGray-300), <alpha-value>)",400:"rgba(var(--colors-warmGray-400), <alpha-value>)",50:"rgba(var(--colors-warmGray-50), <alpha-value>)",500:"rgba(var(--colors-warmGray-500), <alpha-value>)",600:"rgba(var(--colors-warmGray-600), <alpha-value>)",700:"rgba(var(--colors-warmGray-700), <alpha-value>)",800:"rgba(var(--colors-warmGray-800), <alpha-value>)",900:"rgba(var(--colors-warmGray-900), <alpha-value>)"},trueGray:{100:"rgba(var(--colors-trueGray-100), <alpha-value>)",200:"rgba(var(--colors-trueGray-200), <alpha-value>)",300:"rgba(var(--colors-trueGray-300), <alpha-value>)",400:"rgba(var(--colors-trueGray-400), <alpha-value>)",50:"rgba(var(--colors-trueGray-50), <alpha-value>)",500:"rgba(var(--colors-trueGray-500), <alpha-value>)",600:"rgba(var(--colors-trueGray-600), <alpha-value>)",700:"rgba(var(--colors-trueGray-700), <alpha-value>)",800:"rgba(var(--colors-trueGray-800), <alpha-value>)",900:"rgba(var(--colors-trueGray-900), <alpha-value>)"},coolGray:{100:"rgba(var(--colors-coolGray-100), <alpha-value>)",200:"rgba(var(--colors-coolGray-200), <alpha-value>)",300:"rgba(var(--colors-coolGray-300), <alpha-value>)",400:"rgba(var(--colors-coolGray-400), <alpha-value>)",50:"rgba(var(--colors-coolGray-50), <alpha-value>)",500:"rgba(var(--colors-coolGray-500), <alpha-value>)",600:"rgba(var(--colors-coolGray-600), <alpha-value>)",700:"rgba(var(--colors-coolGray-700), <alpha-value>)",800:"rgba(var(--colors-coolGray-800), <alpha-value>)",900:"rgba(var(--colors-coolGray-900), <alpha-value>)"},blueGray:{100:"rgba(var(--colors-blueGray-100), <alpha-value>)",200:"rgba(var(--colors-blueGray-200), <alpha-value>)",300:"rgba(var(--colors-blueGray-300), <alpha-value>)",400:"rgba(var(--colors-blueGray-400), <alpha-value>)",50:"rgba(var(--colors-blueGray-50), <alpha-value>)",500:"rgba(var(--colors-blueGray-500), <alpha-value>)",600:"rgba(var(--colors-blueGray-600), <alpha-value>)",700:"rgba(var(--colors-blueGray-700), <alpha-value>)",800:"rgba(var(--colors-blueGray-800), <alpha-value>)",900:"rgba(var(--colors-blueGray-900), <alpha-value>)"}};describe.each(Object.keys(o))("It does not generate the deprecated Tailwind colors",(a=>{it(`does not generate "${a}" colors`,(()=>{expect((0,e.generateTailwindColors)()).toEqual(expect.not.objectContaining({[a]:o[a]}))}))})),it("generates root CSS variables",(()=>{expect((0,e.generateRootCSSVars)()).toEqual(expect.objectContaining({"--colors-primary-50":"240, 249, 255","--colors-primary-100":"224, 242, 254","--colors-primary-200":"186, 230, 253","--colors-primary-300":"125, 211, 252","--colors-primary-400":"56, 189, 248","--colors-primary-500":"14, 165, 233","--colors-primary-600":"2, 132, 199","--colors-primary-700":"3, 105, 161","--colors-primary-800":"7, 89, 133","--colors-primary-900":"12, 74, 110"}))}))}},a=>{a.O(0,[660],(()=>{return r=3098,a(a.s=r);var r}));a.O()}]);
//# sourceMappingURL=main.js.map