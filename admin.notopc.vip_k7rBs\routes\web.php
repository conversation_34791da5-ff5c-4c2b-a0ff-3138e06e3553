<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use App\Models\MarketHour;
use App\Http\Controllers\HeicController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// ES数据清理接口 - 只保留近7天数据
Route::get('/admin/cleanup-es-data', function() {
    try {
        // 计算7天前的时间戳
        $sevenDaysAgo = time() - (7 * 24 * 60 * 60);
        $sevenDaysAgoDate = date('Y-m-d H:i:s', $sevenDaysAgo);

        // 获取ES客户端
        $es_client = MarketHour::getEsearchClient();

        // 检查ES连接
        if (!$es_client->ping()) {
            return response()->json([
                'status' => 'error',
                'message' => 'ES服务连接失败',
                'data' => []
            ]);
        }

        $results = [];
        $totalDeleted = 0;
        $errors = [];

        // 查询7天前的数据
        $searchParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'range' => [
                        'id' => [
                            'lt' => $sevenDaysAgo  // 小于7天前的时间戳
                        ]
                    ]
                ],
                'size' => 1000,  // 每次处理1000条
                'sort' => [
                    'id' => ['order' => 'asc']
                ]
            ]
        ];

        $results[] = "开始清理ES数据，保留 {$sevenDaysAgoDate} 之后的数据";

        // 分批删除数据
        $batchCount = 0;
        do {
            $batchCount++;
            $response = $es_client->search($searchParams);
            $hits = $response['hits']['hits'] ?? [];
            $hitCount = count($hits);

            if ($hitCount == 0) {
                break;
            }

            $results[] = "第 {$batchCount} 批：找到 {$hitCount} 条过期数据";

            // 批量删除
            $deleteParams = ['body' => []];

            foreach ($hits as $hit) {
                $deleteParams['body'][] = [
                    'delete' => [
                        '_index' => $hit['_index'],
                        '_type' => $hit['_type'],
                        '_id' => $hit['_id']
                    ]
                ];
            }

            if (!empty($deleteParams['body'])) {
                $retryCount = 0;
                $maxRetries = 3;
                $batchSuccess = false;

                while ($retryCount < $maxRetries && !$batchSuccess) {
                    try {
                        $retryCount++;
                        if ($retryCount > 1) {
                            $results[] = "第 {$batchCount} 批重试第 {$retryCount} 次";
                            sleep(2); // 重试前等待2秒
                        }

                        $deleteResponse = $es_client->bulk($deleteParams);

                        // 检查是否有ES级别的错误
                        if (isset($deleteResponse['errors']) && $deleteResponse['errors'] === true) {
                            $results[] = "第 {$batchCount} 批ES返回错误，分析具体失败项";
                        }

                        // 统计删除结果
                        $deleted = 0;
                        $failed = 0;
                        $failedDetails = [];

                        foreach ($deleteResponse['items'] as $index => $item) {
                            if (isset($item['delete']['result']) && $item['delete']['result'] == 'deleted') {
                                $deleted++;
                            } else {
                                $failed++;
                                // 记录失败详情
                                if (isset($item['delete']['error'])) {
                                    $errorType = $item['delete']['error']['type'] ?? 'unknown';
                                    $errorReason = $item['delete']['error']['reason'] ?? 'unknown';
                                    $failedDetails[] = "索引 {$index}: {$errorType} - {$errorReason}";
                                }
                            }
                        }

                        $totalDeleted += $deleted;
                        $results[] = "第 {$batchCount} 批删除结果：成功 {$deleted} 条，失败 {$failed} 条";

                        if ($failed > 0) {
                            $errors[] = "第 {$batchCount} 批有 {$failed} 条删除失败";
                            // 记录前5个失败详情
                            foreach (array_slice($failedDetails, 0, 5) as $detail) {
                                $errors[] = $detail;
                            }
                            if (count($failedDetails) > 5) {
                                $errors[] = "... 还有 " . (count($failedDetails) - 5) . " 个失败项";
                            }

                            // 如果失败率超过50%，尝试重试
                            if ($failed / ($deleted + $failed) > 0.5 && $retryCount < $maxRetries) {
                                $results[] = "第 {$batchCount} 批失败率过高，准备重试";
                                continue;
                            }
                        }

                        $batchSuccess = true;

                    } catch (Exception $e) {
                        $errorMsg = "第 {$batchCount} 批删除异常（重试 {$retryCount}/{$maxRetries}）：" . $e->getMessage();
                        $errors[] = $errorMsg;
                        $results[] = $errorMsg;

                        if ($retryCount >= $maxRetries) {
                            $results[] = "第 {$batchCount} 批重试次数已达上限，跳过此批次";
                            break;
                        }
                    }
                }

                // 如果这批次完全失败，考虑是否继续
                if (!$batchSuccess) {
                    $results[] = "第 {$batchCount} 批处理失败，检查是否继续";
                    // 连续失败3批则停止
                    static $consecutiveFailures = 0;
                    $consecutiveFailures++;
                    if ($consecutiveFailures >= 3) {
                        $results[] = "连续3批失败，停止清理以避免更多问题";
                        break;
                    }
                } else {
                    // 重置连续失败计数
                    static $consecutiveFailures = 0;
                    $consecutiveFailures = 0;
                }
            }

            // 避免无限循环，最多处理100批
            if ($batchCount >= 100) {
                $results[] = "已处理100批数据，停止清理";
                break;
            }

        } while ($hitCount > 0);

        // 获取清理后的统计信息
        $countParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => ['match_all' => (object)[]]
            ]
        ];

        $countResponse = $es_client->count($countParams);
        $remainingCount = $countResponse['count'] ?? 0;

        $results[] = "清理完成！";
        $results[] = "总共删除：{$totalDeleted} 条记录";
        $results[] = "剩余数据：{$remainingCount} 条记录";

        return response()->json([
            'status' => 'success',
            'message' => 'ES数据清理完成',
            'summary' => [
                'seven_days_ago' => $sevenDaysAgoDate,
                'total_deleted' => $totalDeleted,
                'remaining_count' => $remainingCount,
                'batch_count' => $batchCount,
                'error_count' => count($errors)
            ],
            'results' => $results,
            'errors' => $errors
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'ES数据清理失败：' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// ES数据清理预览接口 - 查看7天前的数据统计
Route::get('/admin/preview-es-cleanup', function() {
    try {
        // 计算7天前的时间戳
        $sevenDaysAgo = time() - (7 * 24 * 60 * 60);
        $sevenDaysAgoDate = date('Y-m-d H:i:s', $sevenDaysAgo);

        // 获取ES客户端
        $es_client = MarketHour::getEsearchClient();

        // 检查ES连接
        if (!$es_client->ping()) {
            return response()->json([
                'status' => 'error',
                'message' => 'ES服务连接失败'
            ]);
        }

        // 统计总数据量
        $totalCountParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => ['match_all' => (object)[]]
            ]
        ];
        $totalResponse = $es_client->count($totalCountParams);
        $totalCount = $totalResponse['count'] ?? 0;

        // 统计7天前的数据量
        $oldDataParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'range' => [
                        'id' => [
                            'lt' => $sevenDaysAgo
                        ]
                    ]
                ]
            ]
        ];
        $oldDataResponse = $es_client->count($oldDataParams);
        $oldDataCount = $oldDataResponse['count'] ?? 0;

        // 统计近7天的数据量
        $recentDataParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'range' => [
                        'id' => [
                            'gte' => $sevenDaysAgo
                        ]
                    ]
                ]
            ]
        ];
        $recentDataResponse = $es_client->count($recentDataParams);
        $recentDataCount = $recentDataResponse['count'] ?? 0;

        // 获取最老和最新的数据时间
        $oldestParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => ['match_all' => (object)[]],
                'size' => 1,
                'sort' => [
                    'id' => ['order' => 'asc']
                ]
            ]
        ];
        $oldestResponse = $es_client->search($oldestParams);
        $oldestData = $oldestResponse['hits']['hits'][0]['_source'] ?? null;

        $newestParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => ['match_all' => (object)[]],
                'size' => 1,
                'sort' => [
                    'id' => ['order' => 'desc']
                ]
            ]
        ];
        $newestResponse = $es_client->search($newestParams);
        $newestData = $newestResponse['hits']['hits'][0]['_source'] ?? null;

        return response()->json([
            'status' => 'success',
            'message' => 'ES数据统计预览',
            'cleanup_threshold' => [
                'seven_days_ago_timestamp' => $sevenDaysAgo,
                'seven_days_ago_date' => $sevenDaysAgoDate
            ],
            'data_statistics' => [
                'total_records' => $totalCount,
                'old_data_count' => $oldDataCount,
                'recent_data_count' => $recentDataCount,
                'will_be_deleted' => $oldDataCount,
                'will_be_kept' => $recentDataCount
            ],
            'data_range' => [
                'oldest_record' => $oldestData ? [
                    'timestamp' => $oldestData['id'],
                    'date' => date('Y-m-d H:i:s', $oldestData['id']),
                    'currency' => $oldestData['base-currency'] ?? 'unknown',
                    'period' => $oldestData['period'] ?? 'unknown'
                ] : null,
                'newest_record' => $newestData ? [
                    'timestamp' => $newestData['id'],
                    'date' => date('Y-m-d H:i:s', $newestData['id']),
                    'currency' => $newestData['base-currency'] ?? 'unknown',
                    'period' => $newestData['period'] ?? 'unknown'
                ] : null
            ],
            'cleanup_info' => [
                'deletion_percentage' => $totalCount > 0 ? round(($oldDataCount / $totalCount) * 100, 2) : 0,
                'retention_percentage' => $totalCount > 0 ? round(($recentDataCount / $totalCount) * 100, 2) : 0
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'ES数据预览失败：' . $e->getMessage()
        ]);
    }
});

// ES数据安全清理接口 - 逐条删除模式
Route::get('/admin/cleanup-es-data-safe', function() {
    try {
        // 计算7天前的时间戳
        $sevenDaysAgo = time() - (7 * 24 * 60 * 60);
        $sevenDaysAgoDate = date('Y-m-d H:i:s', $sevenDaysAgo);

        // 获取ES客户端
        $es_client = MarketHour::getEsearchClient();

        // 检查ES连接
        if (!$es_client->ping()) {
            return response()->json([
                'status' => 'error',
                'message' => 'ES服务连接失败'
            ]);
        }

        $results = [];
        $totalDeleted = 0;
        $totalFailed = 0;
        $errors = [];

        // 查询7天前的数据（小批量）
        $searchParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'range' => [
                        'id' => [
                            'lt' => $sevenDaysAgo
                        ]
                    ]
                ],
                'size' => 100,  // 减少批次大小
                'sort' => [
                    'id' => ['order' => 'asc']
                ]
            ]
        ];

        $results[] = "开始安全清理ES数据（逐条删除模式），保留 {$sevenDaysAgoDate} 之后的数据";

        $batchCount = 0;
        do {
            $batchCount++;
            $response = $es_client->search($searchParams);
            $hits = $response['hits']['hits'] ?? [];
            $hitCount = count($hits);

            if ($hitCount == 0) {
                break;
            }

            $results[] = "第 {$batchCount} 批：找到 {$hitCount} 条过期数据，开始逐条删除";

            $batchDeleted = 0;
            $batchFailed = 0;

            // 逐条删除
            foreach ($hits as $index => $hit) {
                try {
                    $deleteParams = [
                        'index' => $hit['_index'],
                        'type' => $hit['_type'],
                        'id' => $hit['_id']
                    ];

                    $deleteResponse = $es_client->delete($deleteParams);

                    if (isset($deleteResponse['result']) && $deleteResponse['result'] == 'deleted') {
                        $batchDeleted++;
                        $totalDeleted++;
                    } else {
                        $batchFailed++;
                        $totalFailed++;
                    }

                    // 每10条记录休息一下，避免ES过载
                    if (($index + 1) % 10 == 0) {
                        usleep(100000); // 休息0.1秒
                    }

                } catch (Exception $e) {
                    $batchFailed++;
                    $totalFailed++;
                    $errors[] = "删除文档 {$hit['_id']} 失败: " . $e->getMessage();
                }
            }

            $results[] = "第 {$batchCount} 批完成：成功 {$batchDeleted} 条，失败 {$batchFailed} 条";

            // 批次间休息
            sleep(1);

            // 限制最大批次数
            if ($batchCount >= 200) {
                $results[] = "已处理200批数据，停止清理";
                break;
            }

        } while ($hitCount > 0);

        // 获取清理后的统计信息
        $countParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => ['match_all' => (object)[]]
            ]
        ];

        $countResponse = $es_client->count($countParams);
        $remainingCount = $countResponse['count'] ?? 0;

        $results[] = "安全清理完成！";
        $results[] = "总共删除：{$totalDeleted} 条记录";
        $results[] = "删除失败：{$totalFailed} 条记录";
        $results[] = "剩余数据：{$remainingCount} 条记录";

        return response()->json([
            'status' => 'success',
            'message' => 'ES数据安全清理完成',
            'summary' => [
                'seven_days_ago' => $sevenDaysAgoDate,
                'total_deleted' => $totalDeleted,
                'total_failed' => $totalFailed,
                'remaining_count' => $remainingCount,
                'batch_count' => $batchCount,
                'error_count' => count($errors)
            ],
            'results' => $results,
            'errors' => array_slice($errors, 0, 50) // 只返回前50个错误
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'ES数据安全清理失败：' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// ES数据清理接口 - 专门清理1min数据，支持指定清理数量
// 用法: /admin/cleanup-es-1min-data?size=1000 (清理1000条)
// 不带size参数则清理1天前的所有数据
Route::get('/admin/cleanup-es-1min-data', function(Illuminate\Http\Request $request) {
    try {
        // 获取size参数，如果没有则默认按时间清理
        $size = $request->get('size', null);
        $sizeLimit = null;

        if ($size !== null) {
            $sizeLimit = intval($size);
            if ($sizeLimit <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'size参数必须是大于0的整数'
                ]);
            }
        }

        // 计算1天前的时间戳（用于时间模式）
        $oneDayAgo = time() - (1 * 24 * 60 * 60);
        $oneDayAgoDate = date('Y-m-d H:i:s', $oneDayAgo);

        // 获取ES客户端
        $es_client = MarketHour::getEsearchClient();

        // 检查ES连接
        if (!$es_client->ping()) {
            return response()->json([
                'status' => 'error',
                'message' => 'ES服务连接失败'
            ]);
        }

        $results = [];
        $totalDeleted = 0;
        $totalFailed = 0;
        $errors = [];

        // 根据是否有size参数决定查询条件
        if ($sizeLimit !== null) {
            // 数量模式：清理指定数量的最老1min数据
            $searchParams = [
                'index' => 'market.kline',
                'type' => '_doc',
                'body' => [
                    'query' => [
                        'match' => [
                            'period' => '1min'  // 只处理1min周期的数据
                        ]
                    ],
                    'size' => min($sizeLimit, 1000),  // 每批最多1000条，但不超过总限制
                    'sort' => [
                        'id' => ['order' => 'asc']  // 从最老的开始删除
                    ]
                ]
            ];
            $results[] = "开始清理1min周期ES数据，删除最老的 {$sizeLimit} 条记录";
        } else {
            // 时间模式：清理1天前的数据
            $searchParams = [
                'index' => 'market.kline',
                'type' => '_doc',
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'range' => [
                                        'id' => [
                                            'lt' => $oneDayAgo  // 小于1天前的时间戳
                                        ]
                                    ]
                                ],
                                [
                                    'match' => [
                                        'period' => '1min'  // 只处理1min周期的数据
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'size' => 500,  // 每批处理500条
                    'sort' => [
                        'id' => ['order' => 'asc']
                    ]
                ]
            ];
            $results[] = "开始清理1min周期ES数据，保留 {$oneDayAgoDate} 之后的数据";
        }

        // 先统计要删除的数据量
        if ($sizeLimit !== null) {
            // 数量模式：统计总的1min数据量
            $countParams = [
                'index' => 'market.kline',
                'type' => '_doc',
                'body' => [
                    'query' => [
                        'match' => [
                            'period' => '1min'
                        ]
                    ]
                ]
            ];
            $countResponse = $es_client->count($countParams);
            $totalAvailable = $countResponse['count'] ?? 0;
            $totalToDelete = min($sizeLimit, $totalAvailable);
            $results[] = "找到 {$totalAvailable} 条1min数据，将删除最老的 {$totalToDelete} 条";
        } else {
            // 时间模式：统计1天前的数据量
            $countParams = [
                'index' => 'market.kline',
                'type' => '_doc',
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'range' => [
                                        'id' => [
                                            'lt' => $oneDayAgo
                                        ]
                                    ]
                                ],
                                [
                                    'match' => [
                                        'period' => '1min'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
            $countResponse = $es_client->count($countParams);
            $totalToDelete = $countResponse['count'] ?? 0;
            $results[] = "找到 {$totalToDelete} 条1min周期的过期数据需要删除";
        }

        if ($totalToDelete == 0) {
            return response()->json([
                'status' => 'success',
                'message' => '没有需要清理的1min数据',
                'summary' => [
                    'mode' => $sizeLimit !== null ? 'size' : 'time',
                    'size_limit' => $sizeLimit,
                    'one_day_ago' => $oneDayAgoDate,
                    'total_to_delete' => 0,
                    'total_deleted' => 0,
                    'total_failed' => 0
                ],
                'results' => $results
            ]);
        }

        // 分批删除数据
        $batchCount = 0;
        $remainingToDelete = $totalToDelete;  // 剩余需要删除的数量

        do {
            $batchCount++;

            // 在数量模式下，调整每批的大小
            if ($sizeLimit !== null) {
                $searchParams['body']['size'] = min(500, $remainingToDelete);
            }

            $response = $es_client->search($searchParams);
            $hits = $response['hits']['hits'] ?? [];
            $hitCount = count($hits);

            if ($hitCount == 0) {
                break;
            }

            $results[] = "第 {$batchCount} 批：找到 {$hitCount} 条1min数据";

            $batchDeleted = 0;
            $batchFailed = 0;

            // 逐条删除（更安全）
            foreach ($hits as $index => $hit) {
                try {
                    $deleteParams = [
                        'index' => $hit['_index'],
                        'type' => $hit['_type'],
                        'id' => $hit['_id']
                    ];

                    $deleteResponse = $es_client->delete($deleteParams);

                    if (isset($deleteResponse['result']) && $deleteResponse['result'] == 'deleted') {
                        $batchDeleted++;
                        $totalDeleted++;
                    } else {
                        $batchFailed++;
                        $totalFailed++;
                        $errors[] = "删除失败: {$hit['_id']} - " . ($deleteResponse['result'] ?? 'unknown');
                    }

                    // 每20条记录休息一下
                    if (($index + 1) % 20 == 0) {
                        usleep(50000); // 休息0.05秒
                    }

                } catch (Exception $e) {
                    $batchFailed++;
                    $totalFailed++;
                    $errors[] = "删除异常: {$hit['_id']} - " . $e->getMessage();
                }
            }

            $results[] = "第 {$batchCount} 批完成：成功 {$batchDeleted} 条，失败 {$batchFailed} 条";

            // 更新剩余需要删除的数量
            if ($sizeLimit !== null) {
                $remainingToDelete -= $batchDeleted;
                if ($remainingToDelete <= 0) {
                    $results[] = "已达到指定删除数量 {$sizeLimit}，停止清理";
                    break;
                }
            }

            // 批次间休息
            sleep(1);

            // 显示进度
            $progress = round(($totalDeleted / $totalToDelete) * 100, 2);
            $results[] = "清理进度：{$totalDeleted}/{$totalToDelete} ({$progress}%)";

            // 限制最大批次数，防止无限循环
            if ($batchCount >= 500) {
                $results[] = "已处理500批数据，停止清理";
                break;
            }

        } while ($hitCount > 0 && ($sizeLimit === null || $remainingToDelete > 0));

        // 获取清理后的统计信息
        $finalCountParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => ['match_all' => (object)[]]
            ]
        ];

        $finalCountResponse = $es_client->count($finalCountParams);
        $remainingTotal = $finalCountResponse['count'] ?? 0;

        // 统计剩余的1min数据
        $remaining1minParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'match' => [
                        'period' => '1min'
                    ]
                ]
            ]
        ];

        $remaining1minResponse = $es_client->count($remaining1minParams);
        $remaining1min = $remaining1minResponse['count'] ?? 0;

        $results[] = "1min数据清理完成！";
        $results[] = "总共删除：{$totalDeleted} 条1min记录";
        $results[] = "删除失败：{$totalFailed} 条1min记录";
        $results[] = "剩余1min数据：{$remaining1min} 条";
        $results[] = "ES总剩余数据：{$remainingTotal} 条";

        return response()->json([
            'status' => 'success',
            'message' => '1min周期ES数据清理完成',
            'summary' => [
                'mode' => $sizeLimit !== null ? 'size' : 'time',
                'size_limit' => $sizeLimit,
                'one_day_ago' => $oneDayAgoDate,
                'total_to_delete' => $totalToDelete,
                'total_deleted' => $totalDeleted,
                'total_failed' => $totalFailed,
                'remaining_1min_count' => $remaining1min,
                'remaining_total_count' => $remainingTotal,
                'batch_count' => $batchCount,
                'error_count' => count($errors),
                'deletion_completed' => $sizeLimit !== null ? ($totalDeleted >= $sizeLimit) : true
            ],
            'results' => $results,
            'errors' => array_slice($errors, 0, 20) // 只返回前20个错误
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => '1min数据清理失败：' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// ES数据预览接口 - 专门查看1min数据统计
Route::get('/admin/preview-es-1min-cleanup', function() {
    try {
        // 计算1天前的时间戳
        $oneDayAgo = time() - (1 * 24 * 60 * 60);
        $oneDayAgoDate = date('Y-m-d H:i:s', $oneDayAgo);

        // 获取ES客户端
        $es_client = MarketHour::getEsearchClient();

        // 检查ES连接
        if (!$es_client->ping()) {
            return response()->json([
                'status' => 'error',
                'message' => 'ES服务连接失败'
            ]);
        }

        // 统计总的1min数据量
        $total1minParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'match' => [
                        'period' => '1min'
                    ]
                ]
            ]
        ];
        $total1minResponse = $es_client->count($total1minParams);
        $total1minCount = $total1minResponse['count'] ?? 0;

        // 统计1天前的1min数据量
        $old1minParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            [
                                'range' => [
                                    'id' => [
                                        'lt' => $oneDayAgo
                                    ]
                                ]
                            ],
                            [
                                'match' => [
                                    'period' => '1min'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $old1minResponse = $es_client->count($old1minParams);
        $old1minCount = $old1minResponse['count'] ?? 0;

        // 统计最近1天的1min数据量
        $recent1minParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            [
                                'range' => [
                                    'id' => [
                                        'gte' => $oneDayAgo
                                    ]
                                ]
                            ],
                            [
                                'match' => [
                                    'period' => '1min'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $recent1minResponse = $es_client->count($recent1minParams);
        $recent1minCount = $recent1minResponse['count'] ?? 0;

        // 获取1min数据的时间范围
        $oldest1minParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'match' => [
                        'period' => '1min'
                    ]
                ],
                'size' => 1,
                'sort' => [
                    'id' => ['order' => 'asc']
                ]
            ]
        ];
        $oldest1minResponse = $es_client->search($oldest1minParams);
        $oldest1minData = $oldest1minResponse['hits']['hits'][0]['_source'] ?? null;

        $newest1minParams = [
            'index' => 'market.kline',
            'type' => '_doc',
            'body' => [
                'query' => [
                    'match' => [
                        'period' => '1min'
                    ]
                ],
                'size' => 1,
                'sort' => [
                    'id' => ['order' => 'desc']
                ]
            ]
        ];
        $newest1minResponse = $es_client->search($newest1minParams);
        $newest1minData = $newest1minResponse['hits']['hits'][0]['_source'] ?? null;

        return response()->json([
            'status' => 'success',
            'message' => '1min周期ES数据统计预览',
            'cleanup_threshold' => [
                'one_day_ago_timestamp' => $oneDayAgo,
                'one_day_ago_date' => $oneDayAgoDate
            ],
            'data_statistics' => [
                'total_1min_records' => $total1minCount,
                'old_1min_count' => $old1minCount,
                'recent_1min_count' => $recent1minCount,
                'will_be_deleted' => $old1minCount,
                'will_be_kept' => $recent1minCount
            ],
            'data_range' => [
                'oldest_1min_record' => $oldest1minData ? [
                    'timestamp' => $oldest1minData['id'],
                    'date' => date('Y-m-d H:i:s', $oldest1minData['id']),
                    'currency' => $oldest1minData['base-currency'] ?? 'unknown'
                ] : null,
                'newest_1min_record' => $newest1minData ? [
                    'timestamp' => $newest1minData['id'],
                    'date' => date('Y-m-d H:i:s', $newest1minData['id']),
                    'currency' => $newest1minData['base-currency'] ?? 'unknown'
                ] : null
            ],
            'cleanup_info' => [
                'deletion_percentage' => $total1minCount > 0 ? round(($old1minCount / $total1minCount) * 100, 2) : 0,
                'retention_percentage' => $total1minCount > 0 ? round(($recent1minCount / $total1minCount) * 100, 2) : 0,
                'estimated_space_saved' => $old1minCount . ' records'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => '1min数据预览失败：' . $e->getMessage()
        ]);
    }
});

// HEIC 图片显示路由 - 动态转换为 JPEG
Route::get('/heic/{path}', function ($path) {
    $decodedPath = base64_decode($path);

    // 检查文件是否存在
    if (!Storage::disk('public')->exists($decodedPath)) {
        abort(404, 'File not found');
    }

    $fullPath = Storage::disk('public')->path($decodedPath);
    $extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));

    // 如果是 HEIC 文件，转换为 JPEG
    if (in_array($extension, ['heic', 'heif'])) {
        try {
            \Log::info('HEIC Display - Converting file', [
                'path' => $decodedPath,
                'full_path' => $fullPath
            ]);

            $jpegContent = \Maestroerror\HeicToJpg::convert($fullPath)->get();

            \Log::info('HEIC Display - Conversion successful', [
                'path' => $decodedPath,
                'jpeg_size' => strlen($jpegContent)
            ]);

            return response($jpegContent)
                ->header('Content-Type', 'image/jpeg')
                ->header('Cache-Control', 'public, max-age=31536000')
                ->header('Content-Disposition', 'inline');

        } catch (\Exception $e) {
            \Log::error('HEIC Display - Conversion failed', [
                'path' => $decodedPath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            abort(500, 'HEIC conversion failed: ' . $e->getMessage());
        }
    }

    // 非 HEIC 文件，直接返回
    return response()->file($fullPath);
})->name('heic.display');
