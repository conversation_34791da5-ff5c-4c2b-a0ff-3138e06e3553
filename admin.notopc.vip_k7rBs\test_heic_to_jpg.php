<?php
/**
 * HEIC 转 JPG 功能测试脚本
 * 基于你提供的简化思路进行测试
 */

echo "🔍 HEIC 转 JPG 功能测试\n";
echo "======================\n\n";

// 1. 检查 ImageMagick 扩展
echo "📊 1. ImageMagick 检查\n";
echo "---------------------\n";

if (!extension_loaded('imagick')) {
    echo "❌ ImageMagick 扩展未安装\n";
    echo "请安装: sudo yum install php-imagick\n";
    exit(1);
}

echo "✅ ImageMagick 扩展已安装\n";

try {
    $imagick = new Imagick();
    $version = $imagick->getVersion();
    echo "版本: " . ($version['versionString'] ?? 'Unknown') . "\n";
    
    // 检查支持的格式
    $formats = $imagick->queryFormats();
    $supportedFormats = array_intersect($formats, ['HEIC', 'HEIF', 'JPEG', 'JPG', 'PNG']);
    echo "支持的格式: " . implode(', ', $supportedFormats) . "\n";
    
    $heicSupported = in_array('HEIC', $formats) || in_array('HEIF', $formats);
    echo "HEIC 支持: " . ($heicSupported ? '✅ 支持' : '❌ 不支持') . "\n";
    
} catch (Exception $e) {
    echo "❌ ImageMagick 初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}
echo "\n";

// 2. 创建测试图片
echo "🖼️ 2. 创建测试图片\n";
echo "-----------------\n";

try {
    // 创建一个简单的测试图片
    $testImage = new Imagick();
    $testImage->newImage(400, 300, new ImagickPixel('#FF6B35'));
    
    // 添加文字
    $draw = new ImagickDraw();
    $draw->setFillColor('#ffffff');
    $draw->setFontSize(24);
    $draw->annotation(100, 150, 'HEIC->JPG TEST');
    $testImage->drawImage($draw);
    
    // 保存为临时 PNG（模拟原始图片）
    $tempDir = sys_get_temp_dir();
    $testPngPath = $tempDir . '/test_source.png';
    $testImage->setImageFormat('png');
    $testImage->writeImage($testPngPath);
    
    echo "✅ 测试图片创建成功: {$testPngPath}\n";
    echo "图片大小: " . filesize($testPngPath) . " bytes\n";
    
    $testImage->destroy();
    
} catch (Exception $e) {
    echo "❌ 创建测试图片失败: " . $e->getMessage() . "\n";
    exit(1);
}
echo "\n";

// 3. 测试转换过程（按照你的思路）
echo "🔄 3. 测试 HEIC 转 JPG 过程\n";
echo "---------------------------\n";

try {
    echo "正在模拟转换过程...\n";
    
    // 步骤1: 创建 Imagick 对象并加载文件
    $image = new Imagick($testPngPath);
    echo "✅ 图片加载成功\n";
    
    // 获取原始信息
    $width = $image->getImageWidth();
    $height = $image->getImageHeight();
    $originalFormat = $image->getImageFormat();
    echo "原始尺寸: {$width}x{$height}\n";
    echo "原始格式: {$originalFormat}\n";
    
    // 步骤2: 设置输出格式为 JPEG
    $image->setImageFormat('jpeg');
    echo "✅ 设置输出格式为 JPEG\n";
    
    // 设置 JPEG 质量
    $image->setImageCompressionQuality(85);
    echo "✅ 设置 JPEG 质量为 85\n";
    
    // 步骤3: 保存为 JPEG 文件
    $outputJpgPath = $tempDir . '/converted_test.jpg';
    $image->writeImage($outputJpgPath);
    echo "✅ 保存为 JPEG 文件: {$outputJpgPath}\n";
    
    // 步骤4: 销毁 Imagick 对象以释放资源
    $image->destroy();
    echo "✅ 释放资源完成\n";
    
    // 验证转换结果
    if (file_exists($outputJpgPath) && filesize($outputJpgPath) > 0) {
        echo "✅ 转换成功！\n";
        echo "输出文件大小: " . filesize($outputJpgPath) . " bytes\n";
        
        // 验证转换后的文件
        $convertedImage = new Imagick($outputJpgPath);
        echo "转换后格式: " . $convertedImage->getImageFormat() . "\n";
        echo "转换后尺寸: " . $convertedImage->getImageWidth() . "x" . $convertedImage->getImageHeight() . "\n";
        $convertedImage->destroy();
        
    } else {
        echo "❌ 转换失败 - 输出文件为空或不存在\n";
    }
    
} catch (Exception $e) {
    echo "❌ 转换过程失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
echo "\n";

// 4. 测试不同质量设置
echo "🎨 4. 测试不同 JPEG 质量\n";
echo "-----------------------\n";

$qualities = [60, 75, 85, 95];
foreach ($qualities as $quality) {
    try {
        $image = new Imagick($testPngPath);
        $image->setImageFormat('jpeg');
        $image->setImageCompressionQuality($quality);
        
        $qualityTestPath = $tempDir . "/test_quality_{$quality}.jpg";
        $image->writeImage($qualityTestPath);
        
        if (file_exists($qualityTestPath)) {
            $size = filesize($qualityTestPath);
            echo "质量 {$quality}: " . number_format($size) . " bytes\n";
            @unlink($qualityTestPath);
        }
        
        $image->destroy();
        
    } catch (Exception $e) {
        echo "质量 {$quality}: ❌ 失败 - " . $e->getMessage() . "\n";
    }
}
echo "\n";

// 5. 测试大图片缩放
echo "📏 5. 测试图片缩放\n";
echo "-----------------\n";

try {
    // 创建一个大图片
    $bigImage = new Imagick();
    $bigImage->newImage(3000, 2000, new ImagickPixel('#4CAF50'));
    
    $draw = new ImagickDraw();
    $draw->setFillColor('#ffffff');
    $draw->setFontSize(48);
    $draw->annotation(1000, 1000, 'BIG IMAGE TEST');
    $bigImage->drawImage($draw);
    
    echo "原始大图尺寸: " . $bigImage->getImageWidth() . "x" . $bigImage->getImageHeight() . "\n";
    
    // 缩放到最大 2048x2048
    $maxWidth = 2048;
    $maxHeight = 2048;
    $bigImage->thumbnailImage($maxWidth, $maxHeight, true);
    
    echo "缩放后尺寸: " . $bigImage->getImageWidth() . "x" . $bigImage->getImageHeight() . "\n";
    
    // 转换为 JPEG
    $bigImage->setImageFormat('jpeg');
    $bigImage->setImageCompressionQuality(85);
    
    $bigJpgPath = $tempDir . '/big_test.jpg';
    $bigImage->writeImage($bigJpgPath);
    
    if (file_exists($bigJpgPath)) {
        echo "✅ 大图转换成功，文件大小: " . number_format(filesize($bigJpgPath)) . " bytes\n";
        @unlink($bigJpgPath);
    }
    
    $bigImage->destroy();
    
} catch (Exception $e) {
    echo "❌ 大图测试失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. 清理测试文件
echo "🧹 6. 清理测试文件\n";
echo "-----------------\n";

$filesToClean = [
    $testPngPath,
    $outputJpgPath ?? null
];

foreach ($filesToClean as $file) {
    if ($file && file_exists($file)) {
        @unlink($file);
        echo "✅ 清理: " . basename($file) . "\n";
    }
}
echo "\n";

// 7. 总结
echo "📊 7. 测试总结\n";
echo "=============\n";

echo "✅ 转换思路验证:\n";
echo "1. 创建 Imagick 对象并加载图片 ✅\n";
echo "2. 设置输出格式为 JPEG ✅\n";
echo "3. 设置 JPEG 质量 ✅\n";
echo "4. 保存为 JPEG 文件 ✅\n";
echo "5. 销毁对象释放资源 ✅\n\n";

echo "💡 优化建议:\n";
echo "- JPEG 质量设置为 85 是一个好的平衡点\n";
echo "- 大图片应该缩放到 2048x2048 以内\n";
echo "- 记得销毁 Imagick 对象释放内存\n";
echo "- 转换后检查文件是否存在且大小 > 0\n\n";

echo "🎯 现在可以测试实际的 HEIC 文件上传了！\n";
?>
