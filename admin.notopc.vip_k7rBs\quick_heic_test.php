<?php
/**
 * 快速 HEIC 测试
 * 简单验证关键功能是否正常
 */

echo "🚀 快速 HEIC 功能测试\n";
echo "====================\n\n";

// 1. 测试扩展名判断
echo "1️⃣ 扩展名判断测试:\n";
$testFiles = ['test.heic', 'test.HEIC', 'test.heif', 'test.jpg'];
foreach ($testFiles as $file) {
    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    $isHeic = in_array($ext, ['heic', 'heif']);
    echo "- {$file} → 扩展名: {$ext} → HEIC: " . ($isHeic ? '✅' : '❌') . "\n";
}
echo "\n";

// 2. 测试 base64 编码/解码
echo "2️⃣ Base64 编码测试:\n";
$testPath = 'image/2024/01/15/photo.heic';
$encoded = base64_encode($testPath);
$decoded = base64_decode($encoded);
echo "- 原始路径: {$testPath}\n";
echo "- 编码后: {$encoded}\n";
echo "- 解码后: {$decoded}\n";
echo "- 匹配: " . ($testPath === $decoded ? '✅' : '❌') . "\n\n";

// 3. 测试 URL 生成逻辑（手动模拟）
echo "3️⃣ URL 生成逻辑测试:\n";
function test_heic_url($path) {
    if (!$path) return '';
    
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
    if (in_array($extension, ['heic', 'heif'])) {
        $encodedPath = base64_encode($path);
        return "http://example.com/heic/{$encodedPath}";
    }
    
    return "http://example.com/storage/{$path}";
}

$testPaths = [
    'image/photo.heic',
    'image/photo.jpg',
    'image/photo.HEIF',
    ''
];

foreach ($testPaths as $path) {
    $url = test_heic_url($path);
    echo "- 路径: '{$path}' → URL: {$url}\n";
}
echo "\n";

// 4. 检查 Laravel 是否正确加载
echo "4️⃣ Laravel 环境检查:\n";
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "- Composer autoload: ✅\n";
    
    require_once __DIR__ . '/vendor/autoload.php';
    
    if (file_exists(__DIR__ . '/bootstrap/app.php')) {
        echo "- Laravel bootstrap: ✅\n";
        
        try {
            $app = require_once __DIR__ . '/bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();
            echo "- Laravel 应用启动: ✅\n";
            
            // 检查 heic_image_url 函数
            if (function_exists('heic_image_url')) {
                echo "- heic_image_url 函数: ✅\n";
                
                $testUrl = heic_image_url('test/sample.heic');
                echo "- 函数测试: {$testUrl}\n";
                
                if (strpos($testUrl, '/heic/') !== false) {
                    echo "- URL 格式: ✅ 正确\n";
                } else {
                    echo "- URL 格式: ❌ 错误\n";
                }
            } else {
                echo "- heic_image_url 函数: ❌ 不存在\n";
            }
            
            // 检查 HeicToJpg 类
            if (class_exists('Maestroerror\HeicToJpg')) {
                echo "- HeicToJpg 类: ✅\n";
            } else {
                echo "- HeicToJpg 类: ❌ 不存在\n";
            }
            
        } catch (Exception $e) {
            echo "- Laravel 启动失败: ❌ " . $e->getMessage() . "\n";
        }
    } else {
        echo "- Laravel bootstrap: ❌ 文件不存在\n";
    }
} else {
    echo "- Composer autoload: ❌ 文件不存在\n";
}
echo "\n";

// 5. 生成测试 URL
echo "5️⃣ 生成测试 URL:\n";
$samplePath = 'image/2024/01/15/sample.heic';
$encodedPath = base64_encode($samplePath);
$testUrl = "http://your-domain.com/heic/{$encodedPath}";

echo "如果你有一个 HEIC 文件在路径: {$samplePath}\n";
echo "那么访问这个 URL 应该能看到转换后的图片:\n";
echo "{$testUrl}\n\n";

echo "🔧 调试步骤:\n";
echo "1. 确保已安装: composer require maestroerror/php-heic-to-jpg\n";
echo "2. 上传一个真实的 HEIC 文件\n";
echo "3. 运行: php debug_heic_issue.php\n";
echo "4. 检查 Laravel 日志: tail -f storage/logs/laravel.log\n";
echo "5. 在浏览器中访问生成的 HEIC URL\n\n";

echo "✅ 快速测试完成！\n";
?>
