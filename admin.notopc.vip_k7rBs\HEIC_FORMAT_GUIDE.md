# HEIC 格式图片上传说明

## 📱 什么是 HEIC 格式？

HEIC (High Efficiency Image Container) 是苹果公司在 iOS 11 中引入的新图片格式，具有更高的压缩效率，但兼容性有限。

## ⚠️ 当前状态

**服务器暂时不支持 HEIC 格式图片的自动转换**

原因：服务器的 ImageMagick 版本不支持 HEIC 格式解码

## 🔧 用户解决方案

### 方案1：iPhone 用户设置（推荐）

1. 打开 iPhone **设置**
2. 找到 **相机**
3. 点击 **格式**
4. 选择 **最兼容** (而不是"高效")

这样拍摄的照片将直接保存为 JPG 格式，无需转换。

### 方案2：手动转换现有 HEIC 图片

#### iPhone 用户：
1. 打开 **照片** 应用
2. 选择要上传的 HEIC 图片
3. 点击 **分享** 按钮
4. 选择 **邮件** 或其他应用
5. 系统会自动转换为 JPG 格式

#### 在线转换工具：
- 搜索 "HEIC转JPG在线转换"
- 推荐工具：
  - iLoveIMG
  - Convertio
  - FreeConvert

### 方案3：使用电脑软件

#### Windows 用户：
- 安装 CopyTrans HEIC
- 或使用 Adobe Photoshop

#### Mac 用户：
- 使用预览应用打开 HEIC 文件
- 导出为 JPG 或 PNG 格式

## ✅ 支持的图片格式

当前系统支持以下格式：
- **JPG/JPEG** ✅ (推荐)
- **PNG** ✅ (推荐)
- **GIF** ✅
- **BMP** ✅

## 🚀 技术解决方案（管理员）

如需启用 HEIC 支持，需要：

### 1. 安装 libheif 库
```bash
# CentOS/RHEL
sudo yum install libheif libheif-devel

# Ubuntu/Debian  
sudo apt-get install libheif-dev libheif1
```

### 2. 重新编译 ImageMagick
```bash
cd /tmp
wget https://github.com/ImageMagick/ImageMagick/archive/7.1.0-16.tar.gz
tar xzf 7.1.0-16.tar.gz
cd ImageMagick-7.1.0-16
./configure --with-heic=yes --enable-shared
make && sudo make install
sudo ldconfig
```

### 3. 重新安装 PHP imagick 扩展
```bash
sudo pecl uninstall imagick
sudo pecl install imagick
sudo systemctl restart php-fpm-82
```

### 4. 验证安装
```bash
php -r "
\$im = new Imagick();
\$formats = \$im->queryFormats();
echo in_array('HEIC', \$formats) ? 'HEIC支持：✅' : 'HEIC支持：❌';
"
```

## 📞 技术支持

如果用户在转换过程中遇到问题，请联系技术支持团队。

---

**更新时间：** 2024-01-15  
**状态：** HEIC 格式暂不支持，建议使用 JPG 格式
