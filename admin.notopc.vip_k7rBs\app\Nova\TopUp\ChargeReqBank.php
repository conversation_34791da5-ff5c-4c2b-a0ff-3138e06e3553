<?php

namespace App\Nova\TopUp;

use App\Nova\Actions\ApplyFilling;
use App\Nova\Actions\ApplyFillingBank;
use App\Nova\Actions\ApplyTurnDown;
use App\Nova\Actions\ApplyTurnDownBank;
use App\Nova\Resource;
use <PERSON>vel\Nova\Fields\Badge;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Image;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class ChargeReqBank extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ChargeReq>
     */
    public static $model = \App\Models\ChargeReqBank::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';



    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static function group(){
        return __('Wallet');
    }


    /**
     * Custom priority level of the resource.
     *
     * @var int
     */
    public static $priority = 2;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'user.account_number',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make(__('AccountNumber'), 'account')->readonly(),
            Text::make(__('CurrencyName'), 'currency')->readonly(),
            Text::make(__('PaymentAddress'), 'payment_address')->readonly(),
            Number::make(__('PaymentAmount'), 'give')->step('any'),
            Number::make(__('USDAmount'), 'amount')->step('any'),
            Image::make(__('EvidenceOfPayment'), 'user_account')
                ->detailWidth(800)
                ->readonly(),
            Badge::make(__('Status'),'status_name')->map([//danger
                'Apply for recharge' => 'info',
                '充值完成' => 'success',
                '驳回申请' => 'danger'
            ]),

            DateTime::make(__('Time Of Application'),'created_at')->readonly(),	# 只读字段
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new ApplyFillingBank)->showInline(),
            (new ApplyTurnDownBank())->showInline(),
        ];
    }

    /**
     * Get the displayble label of the resource.
     *
     * @return string
     */
    public static function label()
    {
        return __('ChargeReqBank');
    }

    /**
     * Get the displayble singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('ChargeReqBank');
    }
}
