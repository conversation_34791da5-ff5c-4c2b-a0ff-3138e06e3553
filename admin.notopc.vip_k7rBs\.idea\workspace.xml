<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0ad43074-7e02-4cb2-9fc3-ac1d18ed5f42" name="更改" comment="es 优化">
      <change afterPath="$PROJECT_DIR$/error.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Console/Commands/CloseCommand.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Console/Commands/CloseCommand.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Console/Kernel.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Console/Kernel.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Models/LeverTransaction.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Models/LeverTransaction.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Models/MarketHour.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Models/MarketHour.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Models/Users.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Models/Users.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Models/UsersWallet.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Models/UsersWallet.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Nova/User/User.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Nova/User/User.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/horizon.php" beforeDir="false" afterPath="$PROJECT_DIR$/config/horizon.php" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/nova-components/Analytics/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/badinansoft/nova-language-switch" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/vendor/predis/predis" />
      <path value="$PROJECT_DIR$/vendor/spatie/macroable" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-icu" />
      <path value="$PROJECT_DIR$/vendor/spatie/url" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/vendor/spatie/laravel-query-builder" />
      <path value="$PROJECT_DIR$/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/spatie/once" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/davidpiesse/nova-toggle" />
      <path value="$PROJECT_DIR$/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/brick/money" />
      <path value="$PROJECT_DIR$/vendor/brick/math" />
      <path value="$PROJECT_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/maatwebsite/excel" />
      <path value="$PROJECT_DIR$/vendor/maatwebsite/laravel-nova-excel" />
      <path value="$PROJECT_DIR$/vendor/leonis/easysms-notification-channel" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/elasticsearch/elasticsearch" />
      <path value="$PROJECT_DIR$/vendor/nova-kit/nova-packages-tool" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/vendor/overtrue/laravel-query-logger" />
      <path value="$PROJECT_DIR$/vendor/overtrue/easy-sms" />
      <path value="$PROJECT_DIR$/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/vendor/overtrue/laravel-pinyin" />
      <path value="$PROJECT_DIR$/vendor/bensampo/laravel-enum" />
      <path value="$PROJECT_DIR$/vendor/mrlaozhou/laravel-extend" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/ezimuel/guzzlestreams" />
      <path value="$PROJECT_DIR$/vendor/bavix/laravel-wallet" />
      <path value="$PROJECT_DIR$/vendor/ezimuel/ringphp" />
      <path value="$PROJECT_DIR$/vendor/openspout/openspout" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/outl1ne/nova-settings" />
      <path value="$PROJECT_DIR$/vendor/outl1ne/nova-translations-loader" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/alphasnow/aliyun-oss-flysystem" />
      <path value="$PROJECT_DIR$/vendor/workerman/workerman" />
      <path value="$PROJECT_DIR$/vendor/alphasnow/aliyun-oss-laravel" />
      <path value="$PROJECT_DIR$/vendor/workerman/gateway-worker" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/giggsey/libphonenumber-for-php" />
      <path value="$PROJECT_DIR$/vendor/giggsey/locale" />
      <path value="$PROJECT_DIR$/vendor/coderello/laravel-passport-social-grant" />
      <path value="$PROJECT_DIR$/vendor/coderello/laravel-nova-lang" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/hashids/hashids" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-diactoros" />
      <path value="$PROJECT_DIR$/vendor/norman-huth/nova-radio-field" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-code" />
      <path value="$PROJECT_DIR$/vendor/norman-huth/nova-basic-package" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/acme/analytics" />
      <path value="$PROJECT_DIR$/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/owenmelbz/nova-radio-field" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/laravie/serialize-queries" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/rap2hpoutre/fast-excel" />
      <path value="$PROJECT_DIR$/vendor/kra8/laravel-snowflake" />
      <path value="$PROJECT_DIR$/vendor/aliyuncs/oss-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/propaganistas/laravel-phone" />
      <path value="$PROJECT_DIR$/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/vendor/mews/captcha" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/league/uri" />
      <path value="$PROJECT_DIR$/vendor/league/oauth1-client" />
      <path value="$PROJECT_DIR$/vendor/eminiarts/nova-tabs" />
      <path value="$PROJECT_DIR$/vendor/league/config" />
      <path value="$PROJECT_DIR$/vendor/league/event" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/manager" />
      <path value="$PROJECT_DIR$/vendor/league/oauth2-server" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/weixin" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/manager" />
      <path value="$PROJECT_DIR$/vendor/socialiteproviders/weixin-web" />
      <path value="$PROJECT_DIR$/vendor/torann/geoip" />
      <path value="$PROJECT_DIR$/vendor/react/promise" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/vendor/laravel/nova" />
      <path value="$PROJECT_DIR$/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/vendor/laravel/horizon" />
      <path value="$PROJECT_DIR$/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/vendor/laravel/passport" />
      <path value="$PROJECT_DIR$/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/vendor/laravel/socialite" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/laravel/ui" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/inertiajs/inertia-laravel" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/defuse/php-encryption" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/vinkla/hashids" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2z81NlDudmvyURbVvJp8pP8weB5" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Code_Projects/btc/notopc_vip/admin.notopc.vip_k7rBs&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code_Projects\btc\notopc_vip\admin.notopc.vip_k7rBs" />
      <recent name="D:\Code_Projects\btc\notopc_vip\admin.notopc.vip_k7rBs\public\storage\logs" />
      <recent name="D:\Code_Projects\btc\notopc_vip\admin.notopc.vip_k7rBs\storage" />
      <recent name="D:\Code_Projects\btc\notopc_vip\admin.notopc.vip_k7rBs\storage\logs" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="主" type="PHPUnitRunConfigurationType" factoryName="PHPUnit">
      <TestRunner configuration_file="$PROJECT_DIR$/phpunit.xml" scope="XML" use_alternative_configuration_file="true" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PS-243.26053.13" />
        <option value="bundled-php-predefined-a98d8de5180a-1ec7b7818973-com.jetbrains.php.sharedIndexes-PS-243.26053.13" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0ad43074-7e02-4cb2-9fc3-ac1d18ed5f42" name="更改" comment="" />
      <created>1751097849410</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751097849410</updated>
      <workItem from="1751097850656" duration="11511000" />
      <workItem from="1751175474182" duration="1530000" />
      <workItem from="1751345471923" duration="8872000" />
      <workItem from="1751436104628" duration="69000" />
      <workItem from="1751436464875" duration="10951000" />
      <workItem from="1751505704955" duration="16131000" />
      <workItem from="1751551815640" duration="1227000" />
      <workItem from="1751598090313" duration="3760000" />
      <workItem from="1751684367827" duration="1592000" />
      <workItem from="1751870053709" duration="1965000" />
      <workItem from="1752247530090" duration="3526000" />
      <workItem from="1752394347161" duration="1380000" />
      <workItem from="1752470044062" duration="1836000" />
      <workItem from="1752481230807" duration="4408000" />
      <workItem from="1752539956930" duration="82000" />
      <workItem from="1752568687597" duration="2905000" />
      <workItem from="1752802945764" duration="12445000" />
      <workItem from="1753282009080" duration="380000" />
      <workItem from="1753515916681" duration="4000" />
      <workItem from="1753775884676" duration="1261000" />
      <workItem from="1753780631640" duration="1201000" />
      <workItem from="1753845521408" duration="909000" />
      <workItem from="1753848271646" duration="1963000" />
      <workItem from="1753857516002" duration="789000" />
      <workItem from="1753974560573" duration="1823000" />
      <workItem from="1754033106556" duration="2069000" />
      <workItem from="1754040517250" duration="1000" />
      <workItem from="1754574476661" duration="18000" />
      <workItem from="1755737361015" duration="5645000" />
      <workItem from="1755761334522" duration="627000" />
      <workItem from="1756965879484" duration="1640000" />
      <workItem from="1757290791723" duration="16967000" />
      <workItem from="1757337623413" duration="5399000" />
      <workItem from="1757379489919" duration="202000" />
      <workItem from="1757379707783" duration="105000" />
      <workItem from="1757379839688" duration="5506000" />
      <workItem from="1757400245975" duration="21257000" />
      <workItem from="1757466388290" duration="8655000" />
      <workItem from="1757488893994" duration="2687000" />
      <workItem from="1757491754124" duration="58000" />
      <workItem from="1757494693405" duration="7403000" />
      <workItem from="1758011363060" duration="2462000" />
    </task>
    <task id="LOCAL-00001" summary="Redis 优化">
      <option name="closed" value="true" />
      <created>1751345557669</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751345557669</updated>
    </task>
    <task id="LOCAL-00002" summary="eth 汇率更新">
      <option name="closed" value="true" />
      <created>1751359777634</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751359777634</updated>
    </task>
    <task id="LOCAL-00003" summary="ES 储存汇率">
      <option name="closed" value="true" />
      <created>1751551845383</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751551845383</updated>
    </task>
    <task id="LOCAL-00004" summary="钱包余额">
      <option name="closed" value="true" />
      <created>1751684387620</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751684387621</updated>
    </task>
    <task id="LOCAL-00005" summary="30D数据问题">
      <option name="closed" value="true" />
      <created>1752486370026</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752486370026</updated>
    </task>
    <task id="LOCAL-00006" summary="时间问题">
      <option name="closed" value="true" />
      <created>1752805642385</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752805642385</updated>
    </task>
    <task id="LOCAL-00007" summary="修复余额bug">
      <option name="closed" value="true" />
      <created>1752822911768</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752822911768</updated>
    </task>
    <task id="LOCAL-00008" summary="配置信息">
      <option name="closed" value="true" />
      <created>1753776305567</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753776305567</updated>
    </task>
    <task id="LOCAL-00009" summary="搜索问题">
      <option name="closed" value="true" />
      <created>1753780924426</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753780924426</updated>
    </task>
    <task id="LOCAL-00010" summary="文案 时间优化">
      <option name="closed" value="true" />
      <created>1755737598463</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755737598463</updated>
    </task>
    <task id="LOCAL-00011" summary="K线数据">
      <option name="closed" value="true" />
      <created>1757315144353</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1757315144354</updated>
    </task>
    <task id="LOCAL-00012" summary="优化程序">
      <option name="closed" value="true" />
      <created>1757413653242</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1757413653242</updated>
    </task>
    <task id="LOCAL-00013" summary="避免ES 失败读取mysql 异常">
      <option name="closed" value="true" />
      <created>1757425405820</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1757425405820</updated>
    </task>
    <task id="LOCAL-00014" summary="es 优化">
      <option name="closed" value="true" />
      <created>1757473432458</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1757473432458</updated>
    </task>
    <option name="localTasksCounter" value="15" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Redis 优化" />
    <MESSAGE value="eth 汇率更新" />
    <MESSAGE value="ES 储存汇率" />
    <MESSAGE value="钱包余额" />
    <MESSAGE value="30D数据问题" />
    <MESSAGE value="时间问题" />
    <MESSAGE value="修复余额bug" />
    <MESSAGE value="配置信息" />
    <MESSAGE value="搜索问题" />
    <MESSAGE value="文案 时间优化" />
    <MESSAGE value="K线数据" />
    <MESSAGE value="优化程序" />
    <MESSAGE value="避免ES 失败读取mysql 异常" />
    <MESSAGE value="es 优化" />
    <option name="LAST_COMMIT_MESSAGE" value="es 优化" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="php">
          <url>file://$PROJECT_DIR$/app/Console/Commands/GetKline.php</url>
          <line>41</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>