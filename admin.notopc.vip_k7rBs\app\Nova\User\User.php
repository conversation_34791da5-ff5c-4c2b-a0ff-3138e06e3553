<?php

namespace App\Nova\User;

use Acme\Analytics\Analytics;
use App\Nova\Actions\UserRecharge;
use App\Nova\Resource;
use Illuminate\Validation\Rules;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Password;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class User extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Users>
     */
    public static $model = \App\Models\Users::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'email', 'account_number'
    ];

    /**
     * Whether to show borders for each column on the X-axis.
     *
     * @var bool
     */
    public static $showColumnBorders = false;

    /**
     * The visual style used for the table. Available options are 'tight' and 'default'.
     *
     * @var string
     */
    public static $tableStyle = 'tight';

    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static function group(){
        return __('User');
    }

    /**
     * Custom priority level of the resource.
     *
     * @var int
     */
    public static $priority = 9999;

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->where('simulation', 0);
    }

    /**
     * Build a "search" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function searchQuery(NovaRequest $request, $query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('id', 'like', '%' . $search . '%')
              ->orWhere('email', 'like', '%' . $search . '%')
              ->orWhere('account_number', 'like', '%' . $search . '%');
        });
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {

        return [
            ID::make()->sortable(),

            Text::make(__('Email'), 'email')
                ->sortable()
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:users,email')
                ->updateRules('unique:users,email,{{resourceId}}'),
            Text::make(__('账号'), 'account_number')
                ->sortable()
                ->rules('required',)
                ->creationRules('unique:users,account_number')
                ->updateRules('unique:users,account_number,{{resourceId}}'),
            Text::make(__('Superior'),'superior')->readonly(),

            Text::make(__('ExtensionCode'), 'extension_code'),

            Text::make(__('UserType'),'my_agent_level')->readonly(),

            Text::make(__('UserLevel'),'level')->readonly(),
            Text::make(__('信用分'), 'score'),
            # Select字段/枚举字段
            Select::make(__('Status'),'status')->options([
                0 => '正常',
                1 => '禁用',
            ])->displayUsingLabels(),

            Number::make(__('Balance'), 'usdt_balance')
                ->step('any')
                ->rules('required', 'numeric', 'min:0')
                ->help('请谨慎修改用户余额，建议通过钱包操作记录进行调整')
                ->displayUsing(function ($value, $resource, $attribute) {
                    try {
                        // 获取用户的USDT钱包余额用于显示
                        $usdtCurrency = \DB::table('currency')->where('name', 'USDT')->where('is_display', 1)->first();
                        if (!$usdtCurrency) {
                            // 如果找不到USDT，尝试查找ID为1的货币
                            $usdtCurrency = \DB::table('currency')->where('id', 1)->first();
                        }

                        if ($usdtCurrency && $resource && $resource->id) {
                            $wallet = \App\Models\UsersWallet::where('user_id', $resource->id)
                                ->where('currency', $usdtCurrency->id)
                                ->first();
                            if ($wallet) {
                                $totalBalance = floatval($wallet->lever_balance) + floatval($wallet->change_balance) + floatval($wallet->micro_balance);
                                return number_format($totalBalance, 8);
                            }
                        }
                    } catch (\Exception $e) {
                        \Log::error('Nova User Balance Display Error: ' . $e->getMessage());
                    }
                    return '0.00000000';
                })
                ->resolveUsing(function ($value, $resource, $attribute) {
                    try {
                        // 获取用户的USDT钱包余额用于编辑
                        $usdtCurrency = \DB::table('currency')->where('name', 'USDT')->where('is_display', 1)->first();
                        if (!$usdtCurrency) {
                            // 如果找不到USDT，尝试查找ID为1的货币
                            $usdtCurrency = \DB::table('currency')->where('id', 1)->first();
                        }

                        if ($usdtCurrency && $resource && $resource->id) {
                            $wallet = \App\Models\UsersWallet::where('user_id', $resource->id)
                                ->where('currency', $usdtCurrency->id)
                                ->first();
                            if ($wallet) {
                                return floatval($wallet->lever_balance) + floatval($wallet->change_balance) + floatval($wallet->micro_balance);
                            }
                        }
                    } catch (\Exception $e) {
                        \Log::error('Nova User Balance Resolve Error: ' . $e->getMessage());
                    }
                    return 0;
                })
                ->fillUsing(function ($request, $model, $attribute, $requestAttribute) {
                    try {
                        // 创建阶段（模型未持久化）不在此处更新余额，避免钱包未创建导致失败
                        if (!$model || !$model->exists || empty($model->id)) {
                            return;
                        }
                        // 更新用户的USDT钱包余额
                        $newBalance = floatval($request->get($requestAttribute));
                        $usdtCurrency = DB::table('currency')->where('name', 'USDT')->where('is_display', 1)->first();
                        if (!$usdtCurrency) {
                            // 如果找不到USDT，尝试查找ID为1的货币
                            $usdtCurrency = DB::table('currency')->where('id', 1)->first();
                        }

                        if ($usdtCurrency && $model && $model->id) {
                            // 确保用户钱包存在（修复创建时钱包不存在的问题）
                            \App\Models\UsersWallet::makeWallet($model->id);

                            $wallet = \App\Models\UsersWallet::where('user_id', $model->id)
                                ->where('currency', $usdtCurrency->id)
                                ->first();

                            if ($wallet) {
                                // 计算当前总余额
                                $currentBalance = floatval($wallet->lever_balance) + floatval($wallet->change_balance) + floatval($wallet->micro_balance);

                                // 计算差额
                                $difference = $newBalance - $currentBalance;

                                // 将差额添加到 change_balance（币币交易余额）
                                $wallet->change_balance = floatval($wallet->change_balance) + $difference;
                                $wallet->save();

                                // 记录操作日志
                                Log::info('Admin updated user balance', [
                                    'user_id' => $model->id,
                                    'old_balance' => $currentBalance,
                                    'new_balance' => $newBalance,
                                    'difference' => $difference,
                                    'currency_id' => $usdtCurrency->id,
                                    'admin_user' => $request->user()->email ?? 'unknown',
                                    'operation' => 'balance_update'
                                ]);
                            } else {
                                Log::warning('Wallet creation failed or not found for user', [
                                    'user_id' => $model->id,
                                    'currency_id' => $usdtCurrency->id
                                ]);
                            }
                        } else {
                            Log::warning('USDT currency not found or invalid model', [
                                'model_id' => $model->id ?? 'null',
                                'currency_found' => $usdtCurrency ? true : false
                            ]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Nova User Balance Update Error: ' . $e->getMessage(), [
                            'user_id' => $model->id ?? 'unknown',
                            'new_balance' => $newBalance ?? 'unknown',
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                })
                ->showOnIndex()
                ->showOnDetail()
                ->showOnCreating()
                ->showOnUpdating(), // 确保在所有视图中都显示

            Password::make(__('Password'), 'password')
                ->onlyOnForms()
                ->creationRules('required', Rules\Password::defaults())
                ->updateRules('nullable', Rules\Password::defaults()),

            // 添加备注字段
            Text::make(__('Label'), 'label')
                ->sortable()
                ->rules('nullable', 'max:30')
                ->hideFromIndex(), # 可以选择隐藏于索引视图中

            DateTime::make(__('Created At'),'created_at')->readonly(),	# 只读字段

            DateTime::make(__('Last Login Time'),'last_login_time')->readonly(),	# 只读字段

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new UserRecharge())->showInline(),
        ];
    }

    /**
     * Get the displayble label of the resource.
     *
     * @return string
     */
    public static function label()
    {
        return __('Users');
    }

    /**
     * Get the displayble singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('Users');
    }

    /**
     * Get the value that should be displayed to represent the resource.
     *
     * @return string
     */
    public function title()
    {
        return $this->name;
    }

    /**
     * Get the search result subtitle for the resource.
     *
     * @return string
     */
    public function subtitle()
    {
        return $this->name . '/' . $this->email;
    }

    /**
     * Handle the "created" event for the user.
     * 用户创建后自动创建钱包
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public static function afterCreate(NovaRequest $request, $model)
    {
        try {
            Log::info('Nova User Created - Starting wallet creation', [
                'user_id' => $model->id,
                'email' => $model->email,
                'account_number' => $model->account_number
            ]);

            // 自动创建用户钱包
            $result = \App\Models\UsersWallet::makeWallet($model->id);

            if ($result) {
                Log::info('Nova User Created - Wallet creation successful', [
                    'user_id' => $model->id,
                    'email' => $model->email
                ]);

                // 检查创建的钱包数量
                $walletCount = \App\Models\UsersWallet::where('user_id', $model->id)->count();
                Log::info('Nova User Created - Wallets created count', [
                    'user_id' => $model->id,
                    'wallet_count' => $walletCount
                ]);

            } else {
                Log::warning('Nova User Created - Wallet creation returned false', [
                    'user_id' => $model->id,
                    'email' => $model->email
                ]);
            }

            // 创建用户档案（如果需要）
            if (!$model->userProfile) {
                \App\Models\UserProfile::unguarded(function () use ($model) {
                    $model->userProfile()->create([]);
                });
                Log::info('Nova User Created - User profile created', [
                    'user_id' => $model->id
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Nova User Created - Error during wallet creation', [
                'user_id' => $model->id ?? 'unknown',
                'email' => $model->email ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 不抛出异常，避免影响用户创建
            // 可以考虑发送通知给管理员
        }
    }

    /**
     * Handle the "updated" event for the user.
     * 用户更新后确保钱包存在
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public static function afterUpdate(NovaRequest $request, $model)
    {
        try {
            // 检查用户是否有钱包，如果没有则创建
            $walletCount = \App\Models\UsersWallet::where('user_id', $model->id)->count();

            if ($walletCount == 0) {
                Log::info('Nova User Updated - No wallets found, creating wallets', [
                    'user_id' => $model->id,
                    'email' => $model->email
                ]);

                \App\Models\UsersWallet::makeWallet($model->id);

                $newWalletCount = \App\Models\UsersWallet::where('user_id', $model->id)->count();
                Log::info('Nova User Updated - Wallets created', [
                    'user_id' => $model->id,
                    'wallet_count' => $newWalletCount
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Nova User Updated - Error during wallet check/creation', [
                'user_id' => $model->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }
}
