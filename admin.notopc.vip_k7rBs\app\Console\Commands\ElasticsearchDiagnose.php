<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MarketHour;

class ElasticsearchDiagnose extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'elasticsearch:diagnose';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose Elasticsearch connection and configuration issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Elasticsearch 诊断开始...');
        $this->newLine();

        // 1. 检查配置
        $this->checkConfiguration();
        $this->newLine();

        // 2. 检查连接
        $this->checkConnection();
        $this->newLine();

        // 3. 检查索引
        $this->checkIndex();
        $this->newLine();

        // 4. 测试查询
        $this->testQuery();
        $this->newLine();

        $this->info('✅ 诊断完成');
    }

    private function checkConfiguration()
    {
        $this->info('📋 1. 检查配置');

        $config = config('elasticsearch.connections.default');
        
        if (empty($config)) {
            $this->error('❌ 没有找到 Elasticsearch 配置');
            $this->warn('💡 请检查 config/elasticsearch.php 文件是否存在');
            return;
        }

        $this->info('✅ 配置文件存在');
        
        $hosts = $config['hosts'] ?? ['http://127.0.0.1:9200'];
        $this->table(['配置项', '值'], [
            ['主机', implode(', ', $hosts)],
            ['用户名', $config['username'] ?? '未设置'],
            ['密码', $config['password'] ? '已设置' : '未设置'],
            ['超时时间', $config['timeout'] ?? '30秒'],
            ['连接超时', $config['connect_timeout'] ?? '10秒'],
            ['重试次数', $config['retries'] ?? '2次'],
        ]);
    }

    private function checkConnection()
    {
        $this->info('🔌 2. 检查连接');

        try {
            $client = MarketHour::getEsearchClient();
            $response = $client->ping();
            
            if ($response) {
                $this->info('✅ ES 连接成功');
                
                // 获取集群信息
                try {
                    $info = $client->info();
                    $this->table(['信息', '值'], [
                        ['集群名称', $info['cluster_name'] ?? 'N/A'],
                        ['版本', $info['version']['number'] ?? 'N/A'],
                        ['状态', '正常'],
                    ]);
                } catch (\Exception $e) {
                    $this->warn('⚠️  无法获取集群详细信息: ' . $e->getMessage());
                }
            } else {
                $this->error('❌ ES 连接失败');
            }
        } catch (\Exception $e) {
            $this->error('❌ ES 连接异常: ' . $e->getMessage());
            $this->warn('💡 可能的原因:');
            $this->warn('   - Elasticsearch 服务未启动');
            $this->warn('   - 主机地址或端口错误');
            $this->warn('   - 网络连接问题');
            $this->warn('   - 认证信息错误');
        }
    }

    private function checkIndex()
    {
        $this->info('📊 3. 检查索引');

        try {
            $client = MarketHour::getEsearchClient();
            
            // 检查 market.kline 索引
            $indexName = 'market.kline';
            $exists = $client->indices()->exists(['index' => $indexName]);
            
            if ($exists) {
                $this->info("✅ 索引 '{$indexName}' 存在");
                
                // 获取索引统计信息
                try {
                    $stats = $client->indices()->stats(['index' => $indexName]);
                    $docCount = $stats['indices'][$indexName]['total']['docs']['count'] ?? 0;
                    $size = $stats['indices'][$indexName]['total']['store']['size_in_bytes'] ?? 0;
                    
                    $this->table(['统计项', '值'], [
                        ['文档数量', number_format($docCount)],
                        ['索引大小', $this->formatBytes($size)],
                    ]);
                } catch (\Exception $e) {
                    $this->warn('⚠️  无法获取索引统计信息: ' . $e->getMessage());
                }
            } else {
                $this->error("❌ 索引 '{$indexName}' 不存在");
                $this->warn('💡 这可能是 ES 查询失败的主要原因');
            }
        } catch (\Exception $e) {
            $this->error('❌ 检查索引时出错: ' . $e->getMessage());
        }
    }

    private function testQuery()
    {
        $this->info('🧪 4. 测试查询');

        try {
            $client = MarketHour::getEsearchClient();
            
            // 测试简单查询
            $params = [
                'index' => 'market.kline',
                'body' => [
                    'query' => [
                        'match_all' => new \stdClass()
                    ],
                    'size' => 1
                ]
            ];
            
            $response = $client->search($params);
            $totalHits = $response['hits']['total']['value'] ?? 0;
            
            if ($totalHits > 0) {
                $this->info("✅ 测试查询成功，找到 {$totalHits} 条记录");
            } else {
                $this->warn('⚠️  测试查询成功，但没有找到数据');
            }
        } catch (\Exception $e) {
            $this->error('❌ 测试查询失败: ' . $e->getMessage());
        }
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
