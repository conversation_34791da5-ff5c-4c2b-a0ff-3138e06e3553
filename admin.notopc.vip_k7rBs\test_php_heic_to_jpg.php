<?php
/**
 * 测试自定义 HeicConverter 服务
 * 验证 HEIC 转换是否正常工作
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\HeicConverter;

echo "🔍 HeicConverter 服务测试\n";
echo "========================\n\n";

// 1. 检查服务是否可用
echo "📦 1. 服务检查\n";
echo "-------------\n";

try {
    $converter = new HeicConverter();
    echo "✅ HeicConverter 服务创建成功\n";
} catch (Exception $e) {
    echo "❌ HeicConverter 服务创建失败: " . $e->getMessage() . "\n";
    exit(1);
}
echo "\n";

// 2. 系统信息检查
echo "💻 2. 系统信息\n";
echo "-------------\n";

$systemInfo = $converter->getSystemInfo();
foreach ($systemInfo as $key => $value) {
    $displayValue = is_bool($value) ? ($value ? '✅ 是' : '❌ 否') : $value;
    echo ucfirst(str_replace('_', ' ', $key)) . ": {$displayValue}\n";
}
echo "\n";

// 3. 检查二进制文件
echo "🔧 3. 二进制文件检查\n";
echo "-------------------\n";

$vendorPath = __DIR__ . '/vendor/maestroerror/heic-to-jpg';
if (is_dir($vendorPath)) {
    echo "✅ 库目录存在: {$vendorPath}\n";
    
    // 检查二进制文件
    $binaries = [
        'php-heic-to-jpg-linux' => $vendorPath . '/bin/php-heic-to-jpg-linux',
        'php-heic-to-jpg-linux-arm64' => $vendorPath . '/bin/php-heic-to-jpg-linux-arm64',
        'php-heic-to-jpg-mac' => $vendorPath . '/bin/php-heic-to-jpg-mac',
        'php-heic-to-jpg-mac-arm64' => $vendorPath . '/bin/php-heic-to-jpg-mac-arm64'
    ];
    
    foreach ($binaries as $name => $path) {
        if (file_exists($path)) {
            $size = filesize($path);
            $permissions = substr(sprintf('%o', fileperms($path)), -4);
            echo "✅ {$name}: 存在 ({$size} bytes, 权限: {$permissions})\n";
        } else {
            echo "❌ {$name}: 不存在\n";
        }
    }
} else {
    echo "❌ 库目录不存在\n";
}
echo "\n";

// 4. 创建测试 HEIC 文件（模拟）
echo "🖼️ 4. 创建测试文件\n";
echo "-----------------\n";

$testDir = __DIR__ . '/storage/app/test';
if (!is_dir($testDir)) {
    mkdir($testDir, 0755, true);
    echo "✅ 创建测试目录: {$testDir}\n";
}

// 创建一个假的 HEIC 文件用于测试（实际上是 PNG）
$testHeicPath = $testDir . '/test_image.heic';
$testJpgPath = $testDir . '/converted_image.jpg';

// 创建一个简单的测试图片数据
$imageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==');
file_put_contents($testHeicPath, $imageData);

echo "✅ 创建测试文件: {$testHeicPath}\n";
echo "文件大小: " . filesize($testHeicPath) . " bytes\n";
echo "\n";

// 5. 测试 isHeic 方法
echo "🔍 5. 测试 isHeic 方法\n";
echo "---------------------\n";

try {
    $isHeicResult = HeicToJpg::isHeic($testHeicPath);
    echo "isHeic 结果: " . ($isHeicResult ? '✅ 是 HEIC' : '❌ 不是 HEIC') . "\n";
    
    // 测试不存在的文件
    $nonExistentFile = $testDir . '/non_existent.heic';
    try {
        $isHeicNonExistent = HeicToJpg::isHeic($nonExistentFile);
        echo "不存在文件测试: " . ($isHeicNonExistent ? '是 HEIC' : '不是 HEIC') . "\n";
    } catch (Exception $e) {
        echo "不存在文件测试: ❌ 异常 - " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ isHeic 方法测试失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. 测试转换功能
echo "🔄 6. 测试转换功能\n";
echo "-----------------\n";

try {
    echo "开始转换测试...\n";
    
    // 根据系统架构选择转换方法
    if ($isArm64) {
        echo "使用 ARM64 转换方法\n";
        HeicToJpg::convert($testHeicPath, "", true)->saveAs($testJpgPath);
    } else {
        echo "使用标准转换方法\n";
        HeicToJpg::convert($testHeicPath)->saveAs($testJpgPath);
    }
    
    // 检查转换结果
    if (file_exists($testJpgPath)) {
        $outputSize = filesize($testJpgPath);
        echo "✅ 转换成功！\n";
        echo "输出文件: {$testJpgPath}\n";
        echo "输出大小: {$outputSize} bytes\n";
        
        // 验证输出文件是否为有效的 JPEG
        $imageInfo = @getimagesize($testJpgPath);
        if ($imageInfo) {
            echo "图片信息: {$imageInfo[0]}x{$imageInfo[1]}, MIME: {$imageInfo['mime']}\n";
        } else {
            echo "⚠️ 输出文件可能不是有效的图片\n";
        }
        
    } else {
        echo "❌ 转换失败 - 输出文件不存在\n";
    }
    
} catch (Exception $e) {
    echo "❌ 转换测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
echo "\n";

// 7. 测试 get 方法
echo "📄 7. 测试 get 方法\n";
echo "------------------\n";

try {
    echo "测试获取转换后的二进制数据...\n";
    
    if ($isArm64) {
        $jpegData = HeicToJpg::convert($testHeicPath, "", true)->get();
    } else {
        $jpegData = HeicToJpg::convert($testHeicPath)->get();
    }
    
    if ($jpegData) {
        echo "✅ 获取二进制数据成功\n";
        echo "数据大小: " . strlen($jpegData) . " bytes\n";
        
        // 保存二进制数据到文件
        $binaryTestPath = $testDir . '/binary_test.jpg';
        file_put_contents($binaryTestPath, $jpegData);
        echo "二进制数据已保存到: {$binaryTestPath}\n";
        
    } else {
        echo "❌ 获取二进制数据失败\n";
    }
    
} catch (Exception $e) {
    echo "❌ get 方法测试失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. 权限检查
echo "🔐 8. 权限检查\n";
echo "-------------\n";

$tempDir = sys_get_temp_dir();
echo "临时目录: {$tempDir}\n";
echo "临时目录可写: " . (is_writable($tempDir) ? '✅ 是' : '❌ 否') . "\n";

$testTempFile = $tempDir . '/heic_test_' . uniqid() . '.tmp';
if (file_put_contents($testTempFile, 'test')) {
    echo "✅ 可以在临时目录创建文件\n";
    @unlink($testTempFile);
} else {
    echo "❌ 无法在临时目录创建文件\n";
}
echo "\n";

// 9. 清理测试文件
echo "🧹 9. 清理测试文件\n";
echo "-----------------\n";

$filesToClean = [
    $testHeicPath,
    $testJpgPath,
    $testDir . '/binary_test.jpg'
];

foreach ($filesToClean as $file) {
    if (file_exists($file)) {
        @unlink($file);
        echo "✅ 清理: " . basename($file) . "\n";
    }
}

// 如果测试目录为空，删除它
if (is_dir($testDir) && count(scandir($testDir)) == 2) {
    @rmdir($testDir);
    echo "✅ 清理测试目录\n";
}
echo "\n";

// 10. 总结
echo "📊 10. 测试总结\n";
echo "==============\n";

echo "✅ 测试完成！\n\n";

echo "💡 使用建议:\n";
echo "1. 确保系统有足够的临时目录权限\n";
echo "2. 对于 ARM64 系统，使用第三个参数 true\n";
echo "3. 转换前使用 isHeic() 方法验证文件格式\n";
echo "4. 处理异常情况，提供友好的错误信息\n\n";

echo "🎯 现在可以测试实际的 HEIC 文件上传了！\n";
echo "上传接口: POST /api/v1/upload\n";
echo "Nova 后台: /admin/resources/user-reals\n";
?>
