<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MarketHour;
use Illuminate\Support\Facades\Cache;

class ElasticsearchMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'elasticsearch:monitor {--interval=60}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor Elasticsearch connection and log connection issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $interval = (int) $this->option('interval');
        
        $this->info("🔍 开始监控 Elasticsearch 连接 (间隔: {$interval}秒)");
        $this->info('按 Ctrl+C 停止监控');
        $this->newLine();

        $successCount = 0;
        $failureCount = 0;
        $startTime = time();

        while (true) {
            $checkTime = now()->format('Y-m-d H:i:s');
            
            try {
                $client = MarketHour::getEsearchClient();
                $result = $client->ping(['timeout' => '2s']);
                
                if ($result) {
                    $successCount++;
                    $this->info("✅ [{$checkTime}] ES 连接正常 (成功: {$successCount}, 失败: {$failureCount})");
                    
                    // 记录成功状态到缓存
                    Cache::put('es_monitor_last_success', time(), 300);
                } else {
                    $failureCount++;
                    $this->error("❌ [{$checkTime}] ES ping 失败 (成功: {$successCount}, 失败: {$failureCount})");
                }
                
            } catch (\Exception $e) {
                $failureCount++;
                $this->error("❌ [{$checkTime}] ES 连接异常: " . $e->getMessage());
                $this->warn("   失败统计: 成功 {$successCount}, 失败 {$failureCount}");
                
                // 记录失败详情
                \Log::warning('ES Monitor: Connection failed', [
                    'error' => $e->getMessage(),
                    'success_count' => $successCount,
                    'failure_count' => $failureCount,
                    'uptime_seconds' => time() - $startTime
                ]);
            }
            
            // 每10次检查显示统计信息
            if (($successCount + $failureCount) % 10 == 0) {
                $uptime = time() - $startTime;
                $successRate = $successCount > 0 ? round(($successCount / ($successCount + $failureCount)) * 100, 2) : 0;
                
                $this->newLine();
                $this->info("📊 统计信息 (运行时间: {$uptime}秒):");
                $this->table(['指标', '值'], [
                    ['成功次数', $successCount],
                    ['失败次数', $failureCount],
                    ['成功率', $successRate . '%'],
                    ['平均间隔', $interval . '秒'],
                ]);
                $this->newLine();
            }
            
            sleep($interval);
        }
    }
}
