<?php
/**
 * HEIC 上传功能测试脚本
 * 测试前端和后台的 HEIC 图片上传转换功能
 */

require_once __DIR__ . '/vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

echo "🔍 HEIC 上传功能测试\n";
echo "====================\n\n";

// 1. 检查 HEIC 支持
echo "📊 1. HEIC 支持检查\n";
echo "-------------------\n";

$heicSupported = false;
if (extension_loaded('imagick')) {
    $imagick = new Imagick();
    $formats = $imagick->queryFormats();
    $heicSupported = in_array('HEIC', $formats) || in_array('HEIF', $formats);
    echo "✅ ImageMagick 已安装\n";
    echo "HEIC 支持: " . ($heicSupported ? '✅ 支持' : '❌ 不支持') . "\n";
} else {
    echo "❌ ImageMagick 未安装\n";
}
echo "\n";

// 2. 创建测试 HEIC 图片（模拟）
echo "🖼️ 2. 创建测试图片\n";
echo "-----------------\n";

if ($heicSupported) {
    try {
        // 创建一个测试 PNG 图片，然后"假装"它是 HEIC
        $testImage = new Imagick();
        $testImage->newImage(400, 300, new ImagickPixel('#4CAF50'));
        
        // 添加文字
        $draw = new ImagickDraw();
        $draw->setFillColor('#ffffff');
        $draw->setFontSize(24);
        $draw->annotation(100, 150, 'HEIC TEST');
        $testImage->drawImage($draw);
        
        $testImage->setImageFormat('png');
        $imageBlob = $testImage->getImageBlob();
        
        // 保存测试图片
        $testPath = 'test_images/test_heic_conversion.png';
        Storage::disk('public')->put($testPath, $imageBlob);
        
        echo "✅ 测试图片创建成功: {$testPath}\n";
        echo "图片大小: " . strlen($imageBlob) . " bytes\n";
        
        $testImage->clear();
        $testImage->destroy();
        
    } catch (Exception $e) {
        echo "❌ 创建测试图片失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ HEIC 不支持，跳过图片创建\n";
}
echo "\n";

// 3. 测试 UtilsController 的转换方法
echo "🔧 3. 测试转换方法\n";
echo "-----------------\n";

if ($heicSupported) {
    try {
        // 创建一个模拟的 HEIC 文件进行转换测试
        $tempDir = sys_get_temp_dir();
        $testHeicPath = $tempDir . '/test_conversion.heic';
        
        // 创建一个简单的测试图片作为 HEIC
        $testImage = new Imagick();
        $testImage->newImage(200, 200, new ImagickPixel('#FF5722'));
        
        $draw = new ImagickDraw();
        $draw->setFillColor('#ffffff');
        $draw->setFontSize(16);
        $draw->annotation(50, 100, 'CONVERSION TEST');
        $testImage->drawImage($draw);
        
        // 保存为 PNG 但命名为 HEIC（用于测试）
        $testImage->setImageFormat('png');
        $testImage->writeImage($testHeicPath);
        
        echo "✅ 测试转换文件创建: {$testHeicPath}\n";
        echo "文件大小: " . filesize($testHeicPath) . " bytes\n";
        
        // 测试转换逻辑
        $convertedPath = $tempDir . '/converted_test.png';
        $image = new Imagick($testHeicPath);
        $image->setImageFormat('png');
        $image->writeImage($convertedPath);
        
        if (file_exists($convertedPath)) {
            echo "✅ 转换测试成功\n";
            echo "转换后大小: " . filesize($convertedPath) . " bytes\n";
            @unlink($convertedPath);
        } else {
            echo "❌ 转换测试失败\n";
        }
        
        $testImage->clear();
        $testImage->destroy();
        @unlink($testHeicPath);
        
    } catch (Exception $e) {
        echo "❌ 转换测试失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ HEIC 不支持，跳过转换测试\n";
}
echo "\n";

// 4. 检查上传接口
echo "🌐 4. 检查上传接口\n";
echo "-----------------\n";

$routes = [
    '/api/v1/upload' => 'App\Http\Controllers\Api\UtilsController@upload',
    '/admin' => 'Nova Admin Panel'
];

foreach ($routes as $route => $controller) {
    echo "- {$route}: {$controller}\n";
}
echo "\n";

// 5. 检查 Nova 字段
echo "🎯 5. 检查 Nova 字段\n";
echo "------------------\n";

$heicImagePath = app_path('Nova/Fields/HeicImage.php');
if (file_exists($heicImagePath)) {
    echo "✅ HeicImage 字段存在: {$heicImagePath}\n";
} else {
    echo "❌ HeicImage 字段不存在\n";
}

$userRealPath = app_path('Nova/User/UserReal.php');
if (file_exists($userRealPath)) {
    $content = file_get_contents($userRealPath);
    if (strpos($content, 'HeicImage') !== false) {
        echo "✅ UserReal 已使用 HeicImage 字段\n";
    } else {
        echo "❌ UserReal 未使用 HeicImage 字段\n";
    }
} else {
    echo "❌ UserReal 文件不存在\n";
}
echo "\n";

// 6. 检查日志配置
echo "📝 6. 检查日志配置\n";
echo "-----------------\n";

$logPath = storage_path('logs/laravel.log');
echo "日志文件: {$logPath}\n";
echo "日志文件存在: " . (file_exists($logPath) ? '✅ 是' : '❌ 否') . "\n";
echo "日志目录可写: " . (is_writable(dirname($logPath)) ? '✅ 是' : '❌ 否') . "\n";
echo "\n";

// 7. 生成测试 URL
echo "🔗 7. 测试 URL\n";
echo "-------------\n";

echo "前端上传测试 URL:\n";
echo "POST " . url('/api/v1/upload') . "\n";
echo "参数: file (HEIC 文件), type=0\n";
echo "需要: Authorization Bearer Token\n\n";

echo "后台管理测试:\n";
echo "1. 访问: " . url('/admin/resources/user-reals') . "\n";
echo "2. 点击任意记录进入详情页\n";
echo "3. 上传 HEIC 格式的身份证图片\n\n";

// 8. 使用建议
echo "💡 8. 使用建议\n";
echo "=============\n";

if (!$heicSupported) {
    echo "❌ HEIC 不支持，需要安装 libheif 库:\n";
    echo "Ubuntu/Debian: sudo apt-get install libheif-dev\n";
    echo "CentOS/RHEL: sudo yum install libheif-devel\n";
    echo "然后重新编译 ImageMagick\n\n";
}

echo "✅ 功能特点:\n";
echo "1. 自动检测 HEIC/HEIF 格式文件\n";
echo "2. 自动转换为 PNG 格式存储\n";
echo "3. 保持原始文件名（扩展名改为 .png）\n";
echo "4. 支持图片压缩（最大 2048x2048）\n";
echo "5. 详细的日志记录\n";
echo "6. 错误处理和回滚机制\n\n";

echo "🔍 测试步骤:\n";
echo "1. 确保 HEIC 支持正常\n";
echo "2. 准备一个 HEIC 格式的图片文件\n";
echo "3. 通过前端或后台上传\n";
echo "4. 检查 storage/logs/laravel.log 中的转换日志\n";
echo "5. 验证存储的文件是否为 PNG 格式\n\n";

echo "📊 预期结果:\n";
echo "- HEIC 文件上传成功\n";
echo "- 自动转换为 PNG 格式\n";
echo "- 图片正常显示\n";
echo "- 日志中有详细的转换记录\n\n";

echo "✅ 测试完成！\n";

// 9. 清理测试文件
echo "🧹 9. 清理测试文件\n";
echo "-----------------\n";

try {
    if (Storage::disk('public')->exists('test_images/test_heic_conversion.png')) {
        Storage::disk('public')->delete('test_images/test_heic_conversion.png');
        echo "✅ 清理测试文件成功\n";
    }
} catch (Exception $e) {
    echo "⚠️ 清理测试文件失败: " . $e->getMessage() . "\n";
}

echo "\n🎯 现在可以测试 HEIC 图片上传功能了！\n";
?>
