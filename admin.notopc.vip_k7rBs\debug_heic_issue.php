<?php
/**
 * HEIC 问题诊断脚本
 * 详细检查每个环节是否正常工作
 */

require_once __DIR__ . '/vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Maestroerror\HeicToJpg;
use Illuminate\Support\Facades\Storage;

echo "🔍 HEIC 问题详细诊断\n";
echo "===================\n\n";

// 1. 检查是否有实际的 HEIC 文件
echo "📁 1. 检查存储中的 HEIC 文件\n";
echo "-----------------------------\n";

$publicPath = storage_path('app/public');
echo "存储路径: {$publicPath}\n";

// 递归查找 HEIC 文件
function findHeicFiles($dir, $depth = 0) {
    $heicFiles = [];
    if ($depth > 3) return $heicFiles; // 限制递归深度
    
    if (is_dir($dir)) {
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') continue;
            
            $fullPath = $dir . '/' . $file;
            if (is_dir($fullPath)) {
                $heicFiles = array_merge($heicFiles, findHeicFiles($fullPath, $depth + 1));
            } else {
                $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                if (in_array($ext, ['heic', 'heif'])) {
                    $relativePath = str_replace(storage_path('app/public/'), '', $fullPath);
                    $heicFiles[] = [
                        'path' => $relativePath,
                        'full_path' => $fullPath,
                        'size' => filesize($fullPath),
                        'extension' => $ext
                    ];
                }
            }
        }
    }
    return $heicFiles;
}

$heicFiles = findHeicFiles($publicPath);

if (empty($heicFiles)) {
    echo "❌ 未找到 HEIC 文件\n";
    echo "请先上传一个 HEIC 文件进行测试\n\n";
    
    // 创建一个测试用的假 HEIC 文件
    $testDir = $publicPath . '/test';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }
    
    $fakeHeicPath = $testDir . '/fake_test.heic';
    file_put_contents($fakeHeicPath, 'fake heic content for testing');
    
    echo "✅ 创建了测试文件: test/fake_test.heic\n";
    $heicFiles[] = [
        'path' => 'test/fake_test.heic',
        'full_path' => $fakeHeicPath,
        'size' => filesize($fakeHeicPath),
        'extension' => 'heic'
    ];
} else {
    echo "✅ 找到 " . count($heicFiles) . " 个 HEIC 文件:\n";
    foreach ($heicFiles as $file) {
        echo "- {$file['path']} ({$file['size']} bytes, .{$file['extension']})\n";
    }
}
echo "\n";

// 2. 测试 heic_image_url 函数
echo "🔧 2. 测试 heic_image_url 函数\n";
echo "------------------------------\n";

if (!function_exists('heic_image_url')) {
    echo "❌ heic_image_url 函数不存在\n";
    echo "请检查 bootstrap/helpers.php 是否正确加载\n\n";
} else {
    echo "✅ heic_image_url 函数存在\n";
    
    // 测试每个找到的 HEIC 文件
    foreach ($heicFiles as $file) {
        $url = heic_image_url($file['path']);
        echo "- 文件: {$file['path']}\n";
        echo "  URL: {$url}\n";
        
        // 检查 URL 格式
        if (strpos($url, '/heic/') !== false) {
            echo "  格式: ✅ 使用 HEIC 路由\n";
            
            // 解码路径验证
            $encodedPath = basename($url);
            $decodedPath = base64_decode($encodedPath);
            echo "  编码路径: {$encodedPath}\n";
            echo "  解码路径: {$decodedPath}\n";
            echo "  路径匹配: " . ($decodedPath === $file['path'] ? '✅' : '❌') . "\n";
        } else {
            echo "  格式: ❌ 未使用 HEIC 路由\n";
        }
        echo "\n";
    }
}

// 3. 测试路由是否存在
echo "🌐 3. 测试 HEIC 路由\n";
echo "-------------------\n";

try {
    $routes = app('router')->getRoutes();
    $heicRoute = null;
    
    foreach ($routes as $route) {
        if ($route->getName() === 'heic.display') {
            $heicRoute = $route;
            break;
        }
    }
    
    if ($heicRoute) {
        echo "✅ HEIC 路由存在\n";
        echo "URI: " . $heicRoute->uri() . "\n";
        echo "名称: " . $heicRoute->getName() . "\n";
        echo "方法: " . implode('|', $heicRoute->methods()) . "\n";
    } else {
        echo "❌ HEIC 路由不存在\n";
        echo "请检查 routes/web.php 中的路由定义\n";
    }
} catch (Exception $e) {
    echo "❌ 路由检查失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. 测试 HeicToJpg 库
echo "📦 4. 测试 HeicToJpg 库\n";
echo "----------------------\n";

if (!class_exists('Maestroerror\HeicToJpg')) {
    echo "❌ HeicToJpg 类不存在\n";
    echo "请运行: composer require maestroerror/php-heic-to-jpg\n";
} else {
    echo "✅ HeicToJpg 类存在\n";
    
    // 测试每个 HEIC 文件
    foreach ($heicFiles as $file) {
        echo "测试文件: {$file['path']}\n";
        
        try {
            $isHeic = HeicToJpg::isHeic($file['full_path']);
            echo "- isHeic(): " . ($isHeic ? '✅ 是 HEIC' : '❌ 不是 HEIC') . "\n";
            
            if ($isHeic) {
                try {
                    $jpegContent = HeicToJpg::convert($file['full_path'])->get();
                    echo "- convert(): ✅ 转换成功 (" . strlen($jpegContent) . " bytes)\n";
                } catch (Exception $e) {
                    echo "- convert(): ❌ 转换失败 - " . $e->getMessage() . "\n";
                }
            }
        } catch (Exception $e) {
            echo "- 测试失败: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
}

// 5. 手动测试路由访问
echo "🌍 5. 手动测试路由访问\n";
echo "---------------------\n";

foreach ($heicFiles as $file) {
    $encodedPath = base64_encode($file['path']);
    $testUrl = "/heic/{$encodedPath}";
    $fullUrl = url($testUrl);
    
    echo "文件: {$file['path']}\n";
    echo "测试 URL: {$fullUrl}\n";
    
    // 尝试模拟路由调用
    try {
        $request = \Illuminate\Http\Request::create($testUrl, 'GET');
        $response = app()->handle($request);
        
        echo "状态码: " . $response->getStatusCode() . "\n";
        echo "内容类型: " . $response->headers->get('Content-Type', '未设置') . "\n";
        echo "内容长度: " . strlen($response->getContent()) . " bytes\n";
        
        if ($response->getStatusCode() === 200) {
            echo "结果: ✅ 路由正常工作\n";
        } else {
            echo "结果: ❌ 路由返回错误\n";
            echo "响应内容: " . substr($response->getContent(), 0, 200) . "\n";
        }
    } catch (Exception $e) {
        echo "路由测试失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// 6. 检查文件权限
echo "🔐 6. 检查文件权限\n";
echo "-----------------\n";

foreach ($heicFiles as $file) {
    $perms = fileperms($file['full_path']);
    $readable = is_readable($file['full_path']);
    
    echo "文件: {$file['path']}\n";
    echo "权限: " . substr(sprintf('%o', $perms), -4) . "\n";
    echo "可读: " . ($readable ? '✅' : '❌') . "\n";
    echo "\n";
}

// 7. 检查扩展名判断逻辑
echo "🔍 7. 扩展名判断逻辑测试\n";
echo "------------------------\n";

$testPaths = [
    'image/test.heic',
    'image/test.HEIC',
    'image/test.heif',
    'image/test.HEIF',
    'image/test.jpg',
    'image/test.png'
];

foreach ($testPaths as $path) {
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
    $isHeicExt = in_array($extension, ['heic', 'heif']);
    
    echo "路径: {$path}\n";
    echo "扩展名: {$extension}\n";
    echo "是否 HEIC: " . ($isHeicExt ? '✅' : '❌') . "\n";
    
    if (function_exists('heic_image_url')) {
        $url = heic_image_url($path);
        $usesHeicRoute = strpos($url, '/heic/') !== false;
        echo "使用 HEIC 路由: " . ($usesHeicRoute ? '✅' : '❌') . "\n";
        echo "生成的 URL: {$url}\n";
    }
    echo "\n";
}

// 8. 总结问题
echo "📊 8. 问题总结\n";
echo "=============\n";

$issues = [];

if (!function_exists('heic_image_url')) {
    $issues[] = "heic_image_url 函数不存在";
}

if (!class_exists('Maestroerror\HeicToJpg')) {
    $issues[] = "HeicToJpg 库未安装";
}

try {
    $routes = app('router')->getRoutes();
    $heicRouteExists = false;
    foreach ($routes as $route) {
        if ($route->getName() === 'heic.display') {
            $heicRouteExists = true;
            break;
        }
    }
    if (!$heicRouteExists) {
        $issues[] = "HEIC 显示路由不存在";
    }
} catch (Exception $e) {
    $issues[] = "路由检查失败";
}

if (empty($issues)) {
    echo "✅ 未发现明显问题\n";
    echo "请检查:\n";
    echo "1. 确保上传的文件确实是 HEIC 格式\n";
    echo "2. 检查浏览器网络请求是否正常\n";
    echo "3. 查看 Laravel 日志文件\n";
} else {
    echo "❌ 发现以下问题:\n";
    foreach ($issues as $issue) {
        echo "- {$issue}\n";
    }
}

echo "\n✅ 诊断完成！\n";
?>
