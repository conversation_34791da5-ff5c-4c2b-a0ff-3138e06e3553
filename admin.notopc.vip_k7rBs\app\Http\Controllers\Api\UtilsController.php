<?php

/**
 * 工具类接口
 */

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Users;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Log;

class UtilsController extends Controller
{
    /**
     * 统一文件图片上传接口 - 支持 HEIC 转换
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        $user = $request->user(); // 获取登录用户信息

        Log::info('Upload API - Request received', [
            'user_id' => $user->id,
            'type' => $request->type,
            'has_file' => $request->hasFile('file')
        ]);

        $request->validate([
            'file' => 'required|file|max:10240', // 最大 10MB
            'type' => 'required|in:0,1', // 类型 0-图片 1-视频文件
        ]);

        // 处理上传类型
        if ($request->type == 1) {
            $type = 'file';
        } else {
            $type = 'image';
        }

        $uploadedFile = $request->file('file');

        // 检查是否为 HEIC 文件
        $originalExtension = strtolower($uploadedFile->getClientOriginalExtension());
        $mimeType = $uploadedFile->getMimeType();

        Log::info('Upload API - File info', [
            'original_name' => $uploadedFile->getClientOriginalName(),
            'extension' => $originalExtension,
            'mime_type' => $mimeType,
            'size' => $uploadedFile->getSize()
        ]);

        // 如果是 HEIC 文件，先转换为 PNG
        if (in_array($originalExtension, ['heic', 'heif']) ||
            in_array($mimeType, ['image/heic', 'image/heif'])) {

            Log::info('Upload API - HEIC file detected, starting conversion');

            $convertedFile = $this->convertHeicToPng($uploadedFile, $user->id);
            if ($convertedFile) {
                $uploadedFile = $convertedFile;
                Log::info('Upload API - HEIC conversion successful');
            } else {
                Log::error('Upload API - HEIC conversion failed');

                // 提供更详细的错误信息
                $errorDetails = [
                    'error' => 'HEIC 图片转换失败',
                    'message' => 'HEIC conversion failed',
                    'suggestions' => [
                        '请将 HEIC 图片转换为 JPG 或 PNG 格式后重新上传',
                        '或者使用其他图片格式'
                    ],
                    'supported_formats' => ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP']
                ];

                return response()->json($errorDetails, 422);
            }
        }

        try {
            $file = upload_images($uploadedFile, $user->id, $type);

            $data['file_path'] = $file->path;
            $data['file_url'] = Storage::disk(config('filesystems.default'))->url($file->path);
            $data['message'] = "";

            Log::info('Upload API - Upload successful', [
                'user_id' => $user->id,
                'file_path' => $file->path,
                'file_url' => $data['file_url']
            ]);

            return response()->json($data, 200);

        } catch (\Exception $e) {
            Log::error('Upload API - Upload failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => '文件上传失败',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成 JSSDK 签名
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function jsSdkSign(Request $request)
    {
        $app = app('easywechat.official_account');
        $utils = $app->getUtils();
        $data = $utils->buildJsSdkConfig(
            url: $request->url, // 提交的网址,需要在微信公众号授权 URL地址 中
        );

        return response()->json($data, 200);
    }

    /**
     * 获取微信 token
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function weixinCode(Request $request)
    {
        $data = Socialite::driver('weixin')->getAccessTokenResponse($request->code);

        return response()->json($data, 200);
    }

    /**
     * 获取指定用户 token
     * 提交 user_id
     * php artisan passport:client --personal
     * @param  Request  $request
     * @return array
     */
    public function getUserToken(Request $request)
    {
        $user_id = $request->user_id;
        $user = Users::find($user_id);
        $token = $user->createToken('api')->accessToken;
        $data = ['token_type' => "Bearer", 'expires_in' => 1296000, 'access_token' => $token, 'user_id' => $user_id];
        return $data;
    }

    /**
     * 将 HEIC 文件转换为 PNG
     * @param \Illuminate\Http\UploadedFile $heicFile
     * @param int $userId
     * @return \Illuminate\Http\UploadedFile|null
     */
    private function convertHeicToPng($heicFile, $userId)
    {
        try {
            Log::info('HEIC Conversion - Starting conversion', [
                'user_id' => $userId,
                'original_name' => $heicFile->getClientOriginalName(),
                'size' => $heicFile->getSize(),
                'mime_type' => $heicFile->getMimeType(),
                'real_path' => $heicFile->getRealPath()
            ]);

            // 检查 ImageMagick 扩展
            if (!extension_loaded('imagick')) {
                Log::error('HEIC Conversion - ImageMagick extension not loaded');
                return null;
            }

            // 检查 ImageMagick 是否支持 HEIC
            $imagick = new \Imagick();
            $formats = $imagick->queryFormats();
            $heicSupported = in_array('HEIC', $formats) || in_array('HEIF', $formats);

            Log::info('HEIC Conversion - Format support check', [
                'heic_supported' => $heicSupported,
                'available_formats' => array_intersect($formats, ['HEIC', 'HEIF', 'JPEG', 'PNG', 'GIF'])
            ]);

            if (!$heicSupported) {
                Log::error('HEIC Conversion - ImageMagick does not support HEIC format', [
                    'available_formats' => array_slice($formats, 0, 10) // 只记录前10个格式
                ]);

                // 尝试使用命令行工具作为备用方案
                return $this->convertHeicWithCli($heicFile, $userId);
            }

            // 检查临时目录
            $tempDir = sys_get_temp_dir();
            if (!is_writable($tempDir)) {
                Log::error('HEIC Conversion - Temp directory not writable', [
                    'temp_dir' => $tempDir,
                    'permissions' => substr(sprintf('%o', fileperms($tempDir)), -4)
                ]);
                return null;
            }

            // 创建临时文件
            $tempHeicPath = tempnam($tempDir, 'heic_upload_') . '.heic';
            $tempPngPath = tempnam($tempDir, 'png_converted_') . '.png';

            Log::info('HEIC Conversion - Temp paths created', [
                'heic_path' => $tempHeicPath,
                'png_path' => $tempPngPath,
                'temp_dir_writable' => is_writable($tempDir)
            ]);

            // 保存 HEIC 文件到临时位置
            try {
                $heicFile->move(dirname($tempHeicPath), basename($tempHeicPath));

                if (!file_exists($tempHeicPath) || filesize($tempHeicPath) == 0) {
                    Log::error('HEIC Conversion - Failed to save temp HEIC file', [
                        'heic_path' => $tempHeicPath,
                        'exists' => file_exists($tempHeicPath),
                        'size' => file_exists($tempHeicPath) ? filesize($tempHeicPath) : 0
                    ]);
                    return null;
                }

                Log::info('HEIC Conversion - Temp HEIC file saved', [
                    'heic_path' => $tempHeicPath,
                    'size' => filesize($tempHeicPath)
                ]);

            } catch (\Exception $e) {
                Log::error('HEIC Conversion - Failed to move uploaded file', [
                    'error' => $e->getMessage(),
                    'heic_path' => $tempHeicPath
                ]);
                return null;
            }

            // 使用 ImageMagick 转换
            try {
                $image = new \Imagick($tempHeicPath);

                // 获取原始尺寸和格式信息
                $originalWidth = $image->getImageWidth();
                $originalHeight = $image->getImageHeight();
                $originalFormat = $image->getImageFormat();

                Log::info('HEIC Conversion - Image loaded successfully', [
                    'width' => $originalWidth,
                    'height' => $originalHeight,
                    'format' => $originalFormat,
                    'colorspace' => $image->getImageColorspace(),
                    'compression' => $image->getImageCompression()
                ]);

            } catch (\ImagickException $e) {
                Log::error('HEIC Conversion - Failed to load image with ImageMagick', [
                    'error' => $e->getMessage(),
                    'heic_path' => $tempHeicPath,
                    'file_exists' => file_exists($tempHeicPath),
                    'file_size' => file_exists($tempHeicPath) ? filesize($tempHeicPath) : 0
                ]);
                @unlink($tempHeicPath);
                return null;
            }

            // 设置输出格式为 PNG
            $image->setImageFormat('png');
            $image->setImageCompressionQuality(90);

            // 如果图片太大，进行压缩
            $maxWidth = 2048;
            $maxHeight = 2048;
            if ($originalWidth > $maxWidth || $originalHeight > $maxHeight) {
                $image->thumbnailImage($maxWidth, $maxHeight, true);
                Log::info('HEIC Conversion - Image resized', [
                    'new_width' => $image->getImageWidth(),
                    'new_height' => $image->getImageHeight()
                ]);
            }

            // 保存转换后的文件
            $image->writeImage($tempPngPath);
            $image->clear();
            $image->destroy();

            // 检查转换结果
            if (!file_exists($tempPngPath) || filesize($tempPngPath) == 0) {
                Log::error('HEIC Conversion - Converted file is empty or does not exist');
                @unlink($tempHeicPath);
                @unlink($tempPngPath);
                return null;
            }

            Log::info('HEIC Conversion - Conversion successful', [
                'converted_size' => filesize($tempPngPath)
            ]);

            // 创建新的 UploadedFile 对象
            $originalName = pathinfo($heicFile->getClientOriginalName(), PATHINFO_FILENAME) . '.png';
            $convertedFile = new \Illuminate\Http\UploadedFile(
                $tempPngPath,
                $originalName,
                'image/png',
                null,
                true // test mode - 不验证文件是否通过 HTTP 上传
            );

            // 清理原始 HEIC 临时文件
            @unlink($tempHeicPath);

            Log::info('HEIC Conversion - UploadedFile created', [
                'new_name' => $originalName,
                'mime_type' => 'image/png'
            ]);

            return $convertedFile;

        } catch (\Exception $e) {
            Log::error('HEIC Conversion - Conversion failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 清理临时文件
            if (isset($tempHeicPath)) @unlink($tempHeicPath);
            if (isset($tempPngPath)) @unlink($tempPngPath);

            return null;
        }
    }

    /**
     * 使用命令行工具转换 HEIC (备用方案)
     * @param \Illuminate\Http\UploadedFile $heicFile
     * @param int $userId
     * @return \Illuminate\Http\UploadedFile|null
     */
    private function convertHeicWithCli($heicFile, $userId)
    {
        try {
            Log::info('HEIC CLI Conversion - Starting CLI conversion', [
                'user_id' => $userId,
                'original_name' => $heicFile->getClientOriginalName()
            ]);

            $tempDir = sys_get_temp_dir();
            $tempHeicPath = tempnam($tempDir, 'cli_heic_') . '.heic';
            $tempPngPath = tempnam($tempDir, 'cli_png_') . '.png';

            // 保存上传文件
            $heicFile->move(dirname($tempHeicPath), basename($tempHeicPath));

            // 尝试不同的命令行工具
            $commands = [
                "magick '{$tempHeicPath}' '{$tempPngPath}' 2>&1",
                "convert '{$tempHeicPath}' '{$tempPngPath}' 2>&1",
                "heif-convert '{$tempHeicPath}' '{$tempPngPath}' 2>&1"
            ];

            $success = false;
            foreach ($commands as $cmd) {
                Log::info('HEIC CLI Conversion - Trying command', ['command' => $cmd]);

                $output = shell_exec($cmd);

                if (file_exists($tempPngPath) && filesize($tempPngPath) > 0) {
                    Log::info('HEIC CLI Conversion - Command successful', [
                        'command' => explode(' ', $cmd)[0],
                        'output_size' => filesize($tempPngPath)
                    ]);
                    $success = true;
                    break;
                } else {
                    Log::warning('HEIC CLI Conversion - Command failed', [
                        'command' => explode(' ', $cmd)[0],
                        'output' => $output
                    ]);
                }
            }

            if (!$success) {
                Log::error('HEIC CLI Conversion - All CLI methods failed');
                @unlink($tempHeicPath);
                @unlink($tempPngPath);
                return null;
            }

            // 创建新的 UploadedFile 对象
            $originalName = pathinfo($heicFile->getClientOriginalName(), PATHINFO_FILENAME) . '.png';
            $convertedFile = new \Illuminate\Http\UploadedFile(
                $tempPngPath,
                $originalName,
                'image/png',
                null,
                true
            );

            @unlink($tempHeicPath);

            Log::info('HEIC CLI Conversion - Conversion successful', [
                'user_id' => $userId,
                'new_name' => $originalName
            ]);

            return $convertedFile;

        } catch (\Exception $e) {
            Log::error('HEIC CLI Conversion - CLI conversion failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            if (isset($tempHeicPath)) @unlink($tempHeicPath);
            if (isset($tempPngPath)) @unlink($tempPngPath);

            return null;
        }
    }
}
