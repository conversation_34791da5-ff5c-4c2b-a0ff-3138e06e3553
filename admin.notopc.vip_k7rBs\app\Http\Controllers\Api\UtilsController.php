<?php

/**
 * 工具类接口
 */

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Users;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Log;

class UtilsController extends Controller
{
    /**
     * 统一文件图片上传接口 - 支持 HEIC 转换
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        $user = $request->user(); // 获取登录用户信息

        Log::info('Upload API - Request received', [
            'user_id' => $user->id,
            'type' => $request->type,
            'has_file' => $request->hasFile('file')
        ]);

        $request->validate([
            'file' => 'required|file|max:10240', // 最大 10MB
            'type' => 'required|in:0,1', // 类型 0-图片 1-视频文件
        ]);

        // 处理上传类型
        if ($request->type == 1) {
            $type = 'file';
        } else {
            $type = 'image';
        }

        $uploadedFile = $request->file('file');

        // 检查是否为 HEIC 文件
        $originalExtension = strtolower($uploadedFile->getClientOriginalExtension());
        $mimeType = $uploadedFile->getMimeType();

        Log::info('Upload API - File info', [
            'original_name' => $uploadedFile->getClientOriginalName(),
            'extension' => $originalExtension,
            'mime_type' => $mimeType,
            'size' => $uploadedFile->getSize()
        ]);

        // 如果是 HEIC 文件，先转换为 PNG
        if (in_array($originalExtension, ['heic', 'heif']) ||
            in_array($mimeType, ['image/heic', 'image/heif'])) {

            Log::info('Upload API - HEIC file detected, starting conversion');

            $convertedFile = $this->convertHeicToPng($uploadedFile, $user->id);
            if ($convertedFile) {
                $uploadedFile = $convertedFile;
                Log::info('Upload API - HEIC conversion successful');
            } else {
                Log::error('Upload API - HEIC conversion failed');
                return response()->json([
                    'error' => 'HEIC 图片转换失败，请尝试其他格式',
                    'message' => 'HEIC conversion failed'
                ], 422);
            }
        }

        try {
            $file = upload_images($uploadedFile, $user->id, $type);

            $data['file_path'] = $file->path;
            $data['file_url'] = Storage::disk(config('filesystems.default'))->url($file->path);
            $data['message'] = "";

            Log::info('Upload API - Upload successful', [
                'user_id' => $user->id,
                'file_path' => $file->path,
                'file_url' => $data['file_url']
            ]);

            return response()->json($data, 200);

        } catch (\Exception $e) {
            Log::error('Upload API - Upload failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => '文件上传失败',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成 JSSDK 签名
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function jsSdkSign(Request $request)
    {
        $app = app('easywechat.official_account');
        $utils = $app->getUtils();
        $data = $utils->buildJsSdkConfig(
            url: $request->url, // 提交的网址,需要在微信公众号授权 URL地址 中
        );

        return response()->json($data, 200);
    }

    /**
     * 获取微信 token
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function weixinCode(Request $request)
    {
        $data = Socialite::driver('weixin')->getAccessTokenResponse($request->code);

        return response()->json($data, 200);
    }

    /**
     * 获取指定用户 token
     * 提交 user_id
     * php artisan passport:client --personal
     * @param  Request  $request
     * @return array
     */
    public function getUserToken(Request $request)
    {
        $user_id = $request->user_id;
        $user = Users::find($user_id);
        $token = $user->createToken('api')->accessToken;
        $data = ['token_type' => "Bearer", 'expires_in' => 1296000, 'access_token' => $token, 'user_id' => $user_id];
        return $data;
    }

    /**
     * 将 HEIC 文件转换为 PNG
     * @param \Illuminate\Http\UploadedFile $heicFile
     * @param int $userId
     * @return \Illuminate\Http\UploadedFile|null
     */
    private function convertHeicToPng($heicFile, $userId)
    {
        try {
            Log::info('HEIC Conversion - Starting conversion', [
                'user_id' => $userId,
                'original_name' => $heicFile->getClientOriginalName(),
                'size' => $heicFile->getSize()
            ]);

            // 检查 ImageMagick 扩展
            if (!extension_loaded('imagick')) {
                Log::error('HEIC Conversion - ImageMagick extension not loaded');
                return null;
            }

            // 检查 ImageMagick 是否支持 HEIC
            $imagick = new \Imagick();
            $formats = $imagick->queryFormats();
            if (!in_array('HEIC', $formats) && !in_array('HEIF', $formats)) {
                Log::error('HEIC Conversion - ImageMagick does not support HEIC format');
                return null;
            }

            // 创建临时文件
            $tempDir = sys_get_temp_dir();
            $tempHeicPath = tempnam($tempDir, 'heic_upload_') . '.heic';
            $tempPngPath = tempnam($tempDir, 'png_converted_') . '.png';

            // 保存 HEIC 文件到临时位置
            $heicFile->move(dirname($tempHeicPath), basename($tempHeicPath));

            Log::info('HEIC Conversion - Temp files created', [
                'heic_path' => $tempHeicPath,
                'png_path' => $tempPngPath,
                'heic_exists' => file_exists($tempHeicPath)
            ]);

            // 使用 ImageMagick 转换
            $image = new \Imagick($tempHeicPath);

            // 获取原始尺寸
            $originalWidth = $image->getImageWidth();
            $originalHeight = $image->getImageHeight();

            Log::info('HEIC Conversion - Image loaded', [
                'width' => $originalWidth,
                'height' => $originalHeight
            ]);

            // 设置输出格式为 PNG
            $image->setImageFormat('png');
            $image->setImageCompressionQuality(90);

            // 如果图片太大，进行压缩
            $maxWidth = 2048;
            $maxHeight = 2048;
            if ($originalWidth > $maxWidth || $originalHeight > $maxHeight) {
                $image->thumbnailImage($maxWidth, $maxHeight, true);
                Log::info('HEIC Conversion - Image resized', [
                    'new_width' => $image->getImageWidth(),
                    'new_height' => $image->getImageHeight()
                ]);
            }

            // 保存转换后的文件
            $image->writeImage($tempPngPath);
            $image->clear();
            $image->destroy();

            // 检查转换结果
            if (!file_exists($tempPngPath) || filesize($tempPngPath) == 0) {
                Log::error('HEIC Conversion - Converted file is empty or does not exist');
                @unlink($tempHeicPath);
                @unlink($tempPngPath);
                return null;
            }

            Log::info('HEIC Conversion - Conversion successful', [
                'converted_size' => filesize($tempPngPath)
            ]);

            // 创建新的 UploadedFile 对象
            $originalName = pathinfo($heicFile->getClientOriginalName(), PATHINFO_FILENAME) . '.png';
            $convertedFile = new \Illuminate\Http\UploadedFile(
                $tempPngPath,
                $originalName,
                'image/png',
                null,
                true // test mode - 不验证文件是否通过 HTTP 上传
            );

            // 清理原始 HEIC 临时文件
            @unlink($tempHeicPath);

            Log::info('HEIC Conversion - UploadedFile created', [
                'new_name' => $originalName,
                'mime_type' => 'image/png'
            ]);

            return $convertedFile;

        } catch (\Exception $e) {
            Log::error('HEIC Conversion - Conversion failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 清理临时文件
            if (isset($tempHeicPath)) @unlink($tempHeicPath);
            if (isset($tempPngPath)) @unlink($tempPngPath);

            return null;
        }
    }
}
