<?php
/**
 * HEIC 转换错误诊断脚本
 * 详细检查 HEIC 转换失败的原因
 */

echo "🔍 HEIC 转换错误诊断\n";
echo "====================\n\n";

// 1. 检查 PHP 扩展
echo "📊 1. PHP 扩展检查\n";
echo "-------------------\n";

$extensions = ['imagick', 'gd', 'fileinfo'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "- {$ext}: " . ($loaded ? '✅ 已安装' : '❌ 未安装') . "\n";
    
    if ($loaded && $ext === 'imagick') {
        try {
            $imagick = new Imagick();
            $version = $imagick->getVersion();
            echo "  版本: " . ($version['versionString'] ?? 'Unknown') . "\n";
            
            // 检查支持的格式
            $formats = $imagick->queryFormats();
            $imageFormats = ['HEIC', 'HEIF', 'JPEG', 'PNG', 'GIF', 'WEBP'];
            echo "  支持的格式:\n";
            foreach ($imageFormats as $format) {
                $supported = in_array($format, $formats);
                echo "    - {$format}: " . ($supported ? '✅' : '❌') . "\n";
            }
            
        } catch (Exception $e) {
            echo "  ❌ ImageMagick 初始化失败: " . $e->getMessage() . "\n";
        }
    }
}
echo "\n";

// 2. 检查系统命令
echo "⚡ 2. 系统命令检查\n";
echo "-------------------\n";

$commands = [
    'magick -version',
    'convert -version', 
    'heif-convert --version',
    'identify -version'
];

foreach ($commands as $cmd) {
    $output = shell_exec($cmd . ' 2>&1');
    $tool = explode(' ', $cmd)[0];
    
    if ($output && !empty(trim($output))) {
        echo "- {$tool}: ✅ 可用\n";
        $lines = explode("\n", trim($output));
        if (!empty($lines[0])) {
            echo "  " . substr($lines[0], 0, 60) . "\n";
        }
    } else {
        echo "- {$tool}: ❌ 不可用\n";
    }
}
echo "\n";

// 3. 创建测试 HEIC 文件
echo "🖼️ 3. 创建测试文件\n";
echo "-----------------\n";

$testSuccess = false;
$testHeicPath = null;

if (extension_loaded('imagick')) {
    try {
        $imagick = new Imagick();
        $formats = $imagick->queryFormats();
        
        if (in_array('HEIC', $formats) || in_array('HEIF', $formats)) {
            // 创建一个简单的测试图片
            $testImage = new Imagick();
            $testImage->newImage(300, 200, new ImagickPixel('#2196F3'));
            
            $draw = new ImagickDraw();
            $draw->setFillColor('#ffffff');
            $draw->setFontSize(20);
            $draw->annotation(50, 100, 'HEIC TEST');
            $testImage->drawImage($draw);
            
            // 尝试保存为不同格式进行测试
            $tempDir = sys_get_temp_dir();
            $testPngPath = $tempDir . '/test_source.png';
            $testHeicPath = $tempDir . '/test_convert.heic';
            
            // 先保存为 PNG
            $testImage->setImageFormat('png');
            $testImage->writeImage($testPngPath);
            
            echo "✅ 测试 PNG 创建成功: {$testPngPath}\n";
            echo "PNG 文件大小: " . filesize($testPngPath) . " bytes\n";
            
            // 尝试转换为 HEIC（如果支持）
            try {
                $testImage->setImageFormat('heic');
                $testImage->writeImage($testHeicPath);
                
                if (file_exists($testHeicPath) && filesize($testHeicPath) > 0) {
                    echo "✅ 测试 HEIC 创建成功: {$testHeicPath}\n";
                    echo "HEIC 文件大小: " . filesize($testHeicPath) . " bytes\n";
                    $testSuccess = true;
                } else {
                    echo "❌ HEIC 文件创建失败或为空\n";
                }
            } catch (Exception $e) {
                echo "❌ HEIC 转换失败: " . $e->getMessage() . "\n";
            }
            
            $testImage->clear();
            $testImage->destroy();
            
        } else {
            echo "❌ ImageMagick 不支持 HEIC 格式\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 测试文件创建失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ ImageMagick 扩展未安装\n";
}
echo "\n";

// 4. 测试 HEIC 读取和转换
echo "🔄 4. 测试 HEIC 转换\n";
echo "-------------------\n";

if ($testSuccess && $testHeicPath && file_exists($testHeicPath)) {
    try {
        echo "正在测试 HEIC 文件读取...\n";
        
        // 尝试读取 HEIC 文件
        $heicImage = new Imagick($testHeicPath);
        
        echo "✅ HEIC 文件读取成功\n";
        echo "图片尺寸: " . $heicImage->getImageWidth() . "x" . $heicImage->getImageHeight() . "\n";
        echo "图片格式: " . $heicImage->getImageFormat() . "\n";
        
        // 尝试转换为 PNG
        $outputPath = sys_get_temp_dir() . '/converted_test.png';
        $heicImage->setImageFormat('png');
        $heicImage->setImageCompressionQuality(90);
        $heicImage->writeImage($outputPath);
        
        if (file_exists($outputPath) && filesize($outputPath) > 0) {
            echo "✅ HEIC 转 PNG 成功: {$outputPath}\n";
            echo "转换后大小: " . filesize($outputPath) . " bytes\n";
            
            // 验证转换后的文件
            $convertedImage = new Imagick($outputPath);
            echo "转换后格式: " . $convertedImage->getImageFormat() . "\n";
            echo "转换后尺寸: " . $convertedImage->getImageWidth() . "x" . $convertedImage->getImageHeight() . "\n";
            
            $convertedImage->clear();
            $convertedImage->destroy();
            @unlink($outputPath);
            
        } else {
            echo "❌ HEIC 转 PNG 失败\n";
        }
        
        $heicImage->clear();
        $heicImage->destroy();
        
    } catch (Exception $e) {
        echo "❌ HEIC 转换测试失败: " . $e->getMessage() . "\n";
        echo "错误文件: " . $e->getFile() . "\n";
        echo "错误行号: " . $e->getLine() . "\n";
    }
} else {
    echo "❌ 没有可用的测试 HEIC 文件\n";
}
echo "\n";

// 5. 检查临时目录权限
echo "📁 5. 临时目录检查\n";
echo "-----------------\n";

$tempDirs = [
    sys_get_temp_dir(),
    '/tmp',
    storage_path('app/temp')
];

foreach ($tempDirs as $dir) {
    if (is_dir($dir)) {
        echo "- {$dir}:\n";
        echo "  存在: ✅\n";
        echo "  可读: " . (is_readable($dir) ? '✅' : '❌') . "\n";
        echo "  可写: " . (is_writable($dir) ? '✅' : '❌') . "\n";
        echo "  权限: " . substr(sprintf('%o', fileperms($dir)), -4) . "\n";
    } else {
        echo "- {$dir}: ❌ 不存在\n";
    }
}
echo "\n";

// 6. 检查内存和资源限制
echo "💾 6. 系统资源检查\n";
echo "-----------------\n";

echo "- PHP 内存限制: " . ini_get('memory_limit') . "\n";
echo "- 最大执行时间: " . ini_get('max_execution_time') . "s\n";
echo "- 最大上传大小: " . ini_get('upload_max_filesize') . "\n";
echo "- POST 最大大小: " . ini_get('post_max_size') . "\n";
echo "- 当前内存使用: " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB\n";
echo "- 内存峰值: " . number_format(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB\n";
echo "\n";

// 7. 模拟上传转换过程
echo "🧪 7. 模拟上传转换\n";
echo "-----------------\n";

if ($testSuccess && $testHeicPath && file_exists($testHeicPath)) {
    try {
        echo "模拟 UtilsController 转换过程...\n";
        
        // 模拟转换方法
        $tempDir = sys_get_temp_dir();
        $tempHeicCopy = tempnam($tempDir, 'heic_upload_') . '.heic';
        $tempPngPath = tempnam($tempDir, 'png_converted_') . '.png';
        
        // 复制测试文件
        copy($testHeicPath, $tempHeicCopy);
        echo "✅ 临时文件创建: {$tempHeicCopy}\n";
        
        // 转换过程
        $image = new Imagick($tempHeicCopy);
        $originalWidth = $image->getImageWidth();
        $originalHeight = $image->getImageHeight();
        
        echo "原始尺寸: {$originalWidth}x{$originalHeight}\n";
        
        $image->setImageFormat('png');
        $image->setImageCompressionQuality(90);
        
        // 检查是否需要缩放
        $maxWidth = 2048;
        $maxHeight = 2048;
        if ($originalWidth > $maxWidth || $originalHeight > $maxHeight) {
            $image->thumbnailImage($maxWidth, $maxHeight, true);
            echo "图片已缩放到: " . $image->getImageWidth() . "x" . $image->getImageHeight() . "\n";
        }
        
        $image->writeImage($tempPngPath);
        $image->clear();
        $image->destroy();
        
        if (file_exists($tempPngPath) && filesize($tempPngPath) > 0) {
            echo "✅ 模拟转换成功\n";
            echo "转换后文件: {$tempPngPath}\n";
            echo "转换后大小: " . filesize($tempPngPath) . " bytes\n";
        } else {
            echo "❌ 模拟转换失败 - 输出文件为空或不存在\n";
        }
        
        // 清理
        @unlink($tempHeicCopy);
        @unlink($tempPngPath);
        
    } catch (Exception $e) {
        echo "❌ 模拟转换失败: " . $e->getMessage() . "\n";
        echo "错误详情:\n";
        echo "  文件: " . $e->getFile() . "\n";
        echo "  行号: " . $e->getLine() . "\n";
        echo "  堆栈: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "❌ 无法进行模拟转换测试\n";
}
echo "\n";

// 8. 清理测试文件
echo "🧹 8. 清理测试文件\n";
echo "-----------------\n";

$filesToClean = [
    $testHeicPath,
    sys_get_temp_dir() . '/test_source.png'
];

foreach ($filesToClean as $file) {
    if ($file && file_exists($file)) {
        @unlink($file);
        echo "✅ 清理: " . basename($file) . "\n";
    }
}
echo "\n";

// 9. 诊断结论和建议
echo "📊 9. 诊断结论\n";
echo "=============\n";

$issues = [];
$suggestions = [];

if (!extension_loaded('imagick')) {
    $issues[] = "ImageMagick PHP 扩展未安装";
    $suggestions[] = "安装 ImageMagick PHP 扩展: sudo apt-get install php-imagick";
}

if (extension_loaded('imagick')) {
    $imagick = new Imagick();
    $formats = $imagick->queryFormats();
    if (!in_array('HEIC', $formats) && !in_array('HEIF', $formats)) {
        $issues[] = "ImageMagick 不支持 HEIC 格式";
        $suggestions[] = "安装 libheif 库: sudo apt-get install libheif-dev";
        $suggestions[] = "重新编译 ImageMagick 并启用 HEIC 支持";
    }
}

$tempDir = sys_get_temp_dir();
if (!is_writable($tempDir)) {
    $issues[] = "临时目录不可写";
    $suggestions[] = "修复临时目录权限: chmod 755 {$tempDir}";
}

if (empty($issues)) {
    echo "✅ 所有检查都通过了！\n";
    echo "HEIC 转换应该可以正常工作。\n\n";
    echo "如果仍然出现错误，请检查:\n";
    echo "1. 上传的文件是否真的是 HEIC 格式\n";
    echo "2. 文件是否损坏\n";
    echo "3. 服务器内存是否足够\n";
    echo "4. Laravel 日志中的详细错误信息\n";
} else {
    echo "❌ 发现以下问题:\n";
    foreach ($issues as $issue) {
        echo "- {$issue}\n";
    }
    echo "\n💡 建议解决方案:\n";
    foreach ($suggestions as $suggestion) {
        echo "- {$suggestion}\n";
    }
}

echo "\n✅ 诊断完成！\n";
?>
