<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HEIC 图片显示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .test-section h3 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: #f9fafb;
        }
        
        .image-item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .image-info {
            font-size: 12px;
            color: #6b7280;
            margin-top: 5px;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            margin-top: 5px;
            display: inline-block;
        }
        
        .status.loading {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h4 {
            color: #1e40af;
            margin-top: 0;
        }
        
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🖼️ HEIC 图片显示测试</h1>
        <p>测试前端 HEIC 转换功能是否正常工作</p>
    </div>

    <div class="instructions">
        <h4>📋 测试说明</h4>
        <ol>
            <li>将 HEIC 格式的图片文件放到 <code>public/storage/test/</code> 目录下</li>
            <li>修改下面的图片路径为你的 HEIC 文件路径</li>
            <li>刷新页面观察转换效果</li>
            <li>查看浏览器控制台的转换日志</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🧪 HEIC 图片转换测试</h3>
        <div class="image-grid">
            <!-- 测试图片 1 -->
            <div class="image-item">
                <img src="/storage/test/sample1.heic" alt="HEIC 测试图片 1" onerror="this.parentElement.querySelector('.status').textContent='文件不存在'; this.parentElement.querySelector('.status').className='status error';">
                <div class="image-info">sample1.heic</div>
                <div class="status loading">等待转换...</div>
            </div>

            <!-- 测试图片 2 -->
            <div class="image-item">
                <img src="/storage/test/sample2.heic" alt="HEIC 测试图片 2" onerror="this.parentElement.querySelector('.status').textContent='文件不存在'; this.parentElement.querySelector('.status').className='status error';">
                <div class="image-info">sample2.heic</div>
                <div class="status loading">等待转换...</div>
            </div>

            <!-- 测试图片 3 -->
            <div class="image-item">
                <img src="/storage/test/sample3.heic" alt="HEIC 测试图片 3" onerror="this.parentElement.querySelector('.status').textContent='文件不存在'; this.parentElement.querySelector('.status').className='status error';">
                <div class="image-info">sample3.heic</div>
                <div class="status loading">等待转换...</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 转换状态监控</h3>
        <div id="stats">
            <p>总图片数: <span id="total-images">0</span></p>
            <p>HEIC 图片数: <span id="heic-images">0</span></p>
            <p>转换成功: <span id="converted-success">0</span></p>
            <p>转换失败: <span id="converted-error">0</span></p>
        </div>
    </div>

    <div class="test-section">
        <h3>📝 转换日志</h3>
        <div id="log" class="log">
            等待日志输出...
        </div>
    </div>

    <!-- 加载 HEIC 显示支持脚本 -->
    <script src="/js/heic-display.js"></script>
    
    <script>
        // 日志输出函数
        function addLog(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 更新统计信息
        function updateStats() {
            const totalImages = document.querySelectorAll('img').length;
            const heicImages = document.querySelectorAll('img[src$=".heic"], img[src$=".HEIC"]').length;
            const convertedSuccess = document.querySelectorAll('.heic-converted').length;
            const convertedError = document.querySelectorAll('.heic-error').length;

            document.getElementById('total-images').textContent = totalImages;
            document.getElementById('heic-images').textContent = heicImages;
            document.getElementById('converted-success').textContent = convertedSuccess;
            document.getElementById('converted-error').textContent = convertedError;
        }

        // 监听转换状态变化
        function observeConversionStatus() {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const img = mutation.target;
                        const statusElement = img.parentElement.querySelector('.status');
                        
                        if (img.classList.contains('heic-converted')) {
                            statusElement.textContent = '转换成功';
                            statusElement.className = 'status success';
                            addLog(`✅ 图片转换成功: ${img.alt}`);
                        } else if (img.classList.contains('heic-error')) {
                            statusElement.textContent = '转换失败';
                            statusElement.className = 'status error';
                            addLog(`❌ 图片转换失败: ${img.alt}`);
                        } else if (img.classList.contains('heic-loading')) {
                            statusElement.textContent = '转换中...';
                            statusElement.className = 'status loading';
                            addLog(`🔄 开始转换: ${img.alt}`);
                        }
                        
                        updateStats();
                    }
                });
            });

            // 监听所有图片元素
            document.querySelectorAll('img').forEach(img => {
                observer.observe(img, { attributes: true, attributeFilter: ['class'] });
            });
        }

        // 重写 console.log 以显示在页面上
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addLog(args.join(' '));
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 HEIC 显示测试页面加载完成');
            updateStats();
            observeConversionStatus();
            
            // 检查 HEIC 支持脚本是否加载
            if (window.HeicDisplay) {
                addLog('✅ HEIC 显示支持脚本加载成功');
            } else {
                addLog('❌ HEIC 显示支持脚本加载失败');
            }
        });

        // 定期更新统计信息
        setInterval(updateStats, 2000);
    </script>
</body>
</html>
