<?php

namespace App\Console\Commands;

use App\Models\DigitalCurrencySet;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UpdateCurrencyExchangeRate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:update-exchange-rate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '每5分钟更新BTC和ETH的汇率';

    /**
     * 火币API配置
     */
    private $huobiApiUrl = 'https://api.huobi.br.com/market/history/kline';

    /**
     * 币种配置
     */
    private $currencies = [
        'btcusdt' => 'BTC',
        'ethusdt' => 'ETH'
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始更新汇率...');

        foreach ($this->currencies as $symbol => $agreement) {
            try {
                $this->updateExchangeRate($symbol, $agreement);
            } catch (\Exception $e) {
                $this->error("更新 {$symbol} 汇率失败: " . $e->getMessage());
                Log::error("UpdateCurrencyExchangeRate Error", [
                    'symbol' => $symbol,
                    'agreement' => $agreement,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info('汇率更新完成');
        return 0;
    }

    /**
     * 更新指定币种的汇率
     *
     * @param string $symbol
     * @param string $agreement
     * @return void
     */
    private function updateExchangeRate($symbol, $agreement)
    {
        // 1. 获取火币API数据
        $apiData = $this->getHuobiKlineData($symbol);

        if (!$apiData) {
            $this->warn("获取 {$symbol} 数据失败");
            return;
        }

        // 2. 解析数据
        $openPrice = $this->parseOpenPrice($apiData);

        if ($openPrice <= 0) {
            $this->warn("{$symbol} 开盘价为0，跳过更新");
            return;
        }

        // 3. 更新数据库
        $updated = $this->updateDatabase($agreement, $openPrice);

        // 4. 更新数字货币地址表的USD价格
        $addressUpdated = $this->updateDigitalCurrencyAddress($agreement, $openPrice);

        if ($updated && $addressUpdated) {
            $this->info("✅ {$symbol} ({$agreement}) 汇率和地址价格更新成功: {$openPrice}");
        } else {
            $this->warn("❌ {$symbol} ({$agreement}) 更新失败 - 汇率:{$updated}, 地址:{$addressUpdated}");
        }
    }

    /**
     * 获取火币K线数据
     *
     * @param string $symbol
     * @return array|null
     */
    private function getHuobiKlineData($symbol)
    {
        try {
            $url = $this->huobiApiUrl;
            $params = [
                'symbol' => $symbol,
                'period' => '1min',
                'size' => 1
            ];

            $this->info("请求火币API: {$symbol}");

            $response = Http::timeout(10)->get($url, $params);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['status'] === 'ok' && !empty($data['data'])) {
                    return $data;
                } else {
                    $this->error("API返回状态异常: " . json_encode($data));
                    return null;
                }
            } else {
                $this->error("API请求失败: HTTP " . $response->status());
                return null;
            }

        } catch (\Exception $e) {
            $this->error("请求火币API异常: " . $e->getMessage());
            Log::error("Huobi API Request Error", [
                'symbol' => $symbol,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 解析开盘价
     *
     * @param array $apiData
     * @return float
     */
    private function parseOpenPrice($apiData)
    {
        if (empty($apiData['data']) || !is_array($apiData['data'])) {
            return 0;
        }

        $klineData = $apiData['data'][0];

        return floatval($klineData['open'] ?? 0);
    }

    /**
     * 更新数据库
     *
     * @param string $agreement
     * @param float $exchangeRate
     * @return bool
     */
    private function updateDatabase($agreement, $exchangeRate)
    {
        try {
            $digitalCurrency = DigitalCurrencySet::where('agreement', $agreement)->first();

            if (!$digitalCurrency) {
                $this->error("未找到协议为 {$agreement} 的数字货币配置");
                return false;
            }

            $oldRate = $digitalCurrency->exchange_rate;
            $digitalCurrency->exchange_rate = $exchangeRate;
            $digitalCurrency->save();

            $this->info("数据库更新成功: {$agreement} {$oldRate} -> {$exchangeRate}");

            // 记录日志
            Log::info("Exchange Rate Updated", [
                'agreement' => $agreement,
                'old_rate' => $oldRate,
                'new_rate' => $exchangeRate,
                'updated_at' => now()
            ]);

            return true;

        } catch (\Exception $e) {
            $this->error("数据库更新失败: " . $e->getMessage());
            Log::error("Database Update Error", [
                'agreement' => $agreement,
                'exchange_rate' => $exchangeRate,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新数字货币地址表的USD价格
     *
     * @param string $agreement
     * @param float $usdPrice
     * @return bool
     */
    private function updateDigitalCurrencyAddress($agreement, $usdPrice)
    {
        try {
            // 更新 digital_currency_address 表中对应协议的 usd_price
            $affected = DB::table('digital_currency_address')
                ->where('agreement', $agreement)
                ->update([
                    'usd_price' => $usdPrice,
                    'updated_at' => now()
                ]);

            if ($affected > 0) {
                $this->info("数字货币地址价格更新成功: {$agreement} 影响 {$affected} 条记录");

                // 记录日志
                \Log::info("Digital Currency Address Price Updated", [
                    'agreement' => $agreement,
                    'usd_price' => $usdPrice,
                    'affected_rows' => $affected,
                    'updated_at' => now()
                ]);

                return true;
            } else {
                $this->warn("未找到协议为 {$agreement} 的数字货币地址记录");
                return true; // 没有记录也算成功，不影响主流程
            }

        } catch (\Exception $e) {
            $this->error("数字货币地址价格更新失败: " . $e->getMessage());
            \Log::error("Digital Currency Address Update Error", [
                'agreement' => $agreement,
                'usd_price' => $usdPrice,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
