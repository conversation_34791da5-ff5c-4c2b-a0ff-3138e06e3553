<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;

class QueueServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 监听任务失败事件，只记录日志，不写入数据库或Redis
        Queue::failing(function (JobFailed $event) {
            // 只记录关键信息到日志，不存储到数据库
            Log::warning('Queue job failed (not stored)', [
                'job' => $event->job->getName(),
                'connection' => $event->connectionName,
                'queue' => $event->job->getQueue(),
                'exception' => $event->exception->getMessage(),
                'attempts' => $event->job->attempts(),
                // 不记录完整的异常堆栈，减少日志大小
            ]);
        });
    }
}
