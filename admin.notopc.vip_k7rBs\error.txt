[2025-09-08 13:31:43] local.ERROR: SQLSTATE[40001]: Serialization failure: 1213 <PERSON><PERSON> found when trying to get lock; try restarting transaction (Connection: mysql, SQL: update `lever_transaction` set `status` = 2, `handle_time` = 1757352703.1337 where `status` = 1 and (`legal` = 1 and `currency` = 106) and ((`type` = 1 and ((`update_price` >= `target_profit_price` and `target_profit_price` > 0) or (`update_price` <= `stop_loss_price` and `stop_loss_price` > 0))) or (`type` = 2 and ((`update_price` <= `target_profit_price` and `target_profit_price` > 0) or (`update_price` >= `stop_loss_price` and `stop_loss_price` > 0))))) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 40001): SQLSTATE[40001]: Serialization failure: 1213 Deadlock found when trying to get lock; try restarting transaction (Connection: mysql, SQL: update `lever_transaction` set `status` = 2, `handle_time` = 1757352703.1337 where `status` = 1 and (`legal` = 1 and `currency` = 106) and ((`type` = 1 and ((`update_price` >= `target_profit_price` and `target_profit_price` > 0) or (`update_price` <= `stop_loss_price` and `stop_loss_price` > 0))) or (`type` = 2 and ((`update_price` <= `target_profit_price` and `target_profit_price` > 0) or (`update_price` >= `stop_loss_price` and `stop_loss_price` > 0))))) at /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:801)
[stacktrace]
#0 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback()
#1 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(593): Illuminate\\Database\\Connection->run()
#2 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(545): Illuminate\\Database\\Connection->affectingStatement()
#3 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3421): Illuminate\\Database\\Connection->update()
#4 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1057): Illuminate\\Database\\Query\\Builder->update()
#5 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(679): Illuminate\\Database\\Eloquent\\Builder->update()
#6 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(256): App\\Models\\LeverTransaction::checkNeedStopPriceTrade()
#7 /www/wwwroot/admin.notopc.vip/app/Jobs/LeverHandle.php(38): App\\Models\\LeverTransaction::tradeHandle()
#8 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\LeverHandle->handle()
#9 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#11 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#13 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()
#14 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()
#15 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()
#17 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow()
#18 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()
#19 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then()
#21 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()
#22 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()
#23 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#24 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()
#25 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()
#26 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()
#27 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#28 /www/wwwroot/admin.notopc.vip/vendor/laravel/horizon/src/Console/WorkCommand.php(51): Illuminate\\Queue\\Console\\WorkCommand->handle()
#29 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Horizon\\Console\\WorkCommand->handle()
#30 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#32 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#33 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#34 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#35 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#36 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#37 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()
#38 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()
#39 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()
#40 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#41 /www/wwwroot/admin.notopc.vip/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#42 {main}

[previous exception] [object] (PDOException(code: 40001): SQLSTATE[40001]: Serialization failure: 1213 Deadlock found when trying to get lock; try restarting transaction at /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:605)
[stacktrace]
#0 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(605): PDOStatement->execute()
#1 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(788): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback()
#3 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(593): Illuminate\\Database\\Connection->run()
#4 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(545): Illuminate\\Database\\Connection->affectingStatement()
#5 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3421): Illuminate\\Database\\Connection->update()
#6 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1057): Illuminate\\Database\\Query\\Builder->update()
#7 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(679): Illuminate\\Database\\Eloquent\\Builder->update()
#8 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(256): App\\Models\\LeverTransaction::checkNeedStopPriceTrade()
#9 /www/wwwroot/admin.notopc.vip/app/Jobs/LeverHandle.php(38): App\\Models\\LeverTransaction::tradeHandle()
#10 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\LeverHandle->handle()
#11 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#13 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#14 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#15 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()
#16 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()
#17 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()
#19 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow()
#20 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()
#21 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then()
#23 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()
#24 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()
#25 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#26 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()
#27 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()
#28 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()
#29 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#30 /www/wwwroot/admin.notopc.vip/vendor/laravel/horizon/src/Console/WorkCommand.php(51): Illuminate\\Queue\\Console\\WorkCommand->handle()
#31 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Horizon\\Console\\WorkCommand->handle()
#32 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#34 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#36 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#37 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#38 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()
#40 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()
#41 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()
#42 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#43 /www/wwwroot/admin.notopc.vip/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 {main}
"}


[2025-09-08 15:06:31] local.ERROR: SQLSTATE[40001]: Serialization failure: 1213 Deadlock found when trying to get lock; try restarting transaction (Connection: mysql, SQL: update `lever_transaction` set `status` = 2, `handle_time` = 1757358391.6806 where `status` = 1 and (`legal` = 1 and `currency` = 103) and ((`type` = 1 and ((`update_price` >= `target_profit_price` and `target_profit_price` > 0) or (`update_price` <= `stop_loss_price` and `stop_loss_price` > 0))) or (`type` = 2 and ((`update_price` <= `target_profit_price` and `target_profit_price` > 0) or (`update_price` >= `stop_loss_price` and `stop_loss_price` > 0))))) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 40001): SQLSTATE[40001]: Serialization failure: 1213 Deadlock found when trying to get lock; try restarting transaction (Connection: mysql, SQL: update `lever_transaction` set `status` = 2, `handle_time` = 1757358391.6806 where `status` = 1 and (`legal` = 1 and `currency` = 103) and ((`type` = 1 and ((`update_price` >= `target_profit_price` and `target_profit_price` > 0) or (`update_price` <= `stop_loss_price` and `stop_loss_price` > 0))) or (`type` = 2 and ((`update_price` <= `target_profit_price` and `target_profit_price` > 0) or (`update_price` >= `stop_loss_price` and `stop_loss_price` > 0))))) at /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:801)
[stacktrace]
#0 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback()
#1 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(593): Illuminate\\Database\\Connection->run()
#2 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(545): Illuminate\\Database\\Connection->affectingStatement()
#3 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3421): Illuminate\\Database\\Connection->update()
#4 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1057): Illuminate\\Database\\Query\\Builder->update()
#5 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(679): Illuminate\\Database\\Eloquent\\Builder->update()
#6 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(256): App\\Models\\LeverTransaction::checkNeedStopPriceTrade()
#7 /www/wwwroot/admin.notopc.vip/app/Jobs/LeverHandle.php(38): App\\Models\\LeverTransaction::tradeHandle()
#8 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\LeverHandle->handle()
#9 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#11 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#13 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()
#14 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()
#15 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()
#17 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow()
#18 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()
#19 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then()
#21 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()
#22 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()
#23 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#24 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()
#25 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()
#26 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()
#27 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#28 /www/wwwroot/admin.notopc.vip/vendor/laravel/horizon/src/Console/WorkCommand.php(51): Illuminate\\Queue\\Console\\WorkCommand->handle()
#29 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Horizon\\Console\\WorkCommand->handle()
#30 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#32 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#33 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#34 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#35 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#36 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#37 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()
#38 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()
#39 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()
#40 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#41 /www/wwwroot/admin.notopc.vip/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#42 {main}

[previous exception] [object] (PDOException(code: 40001): SQLSTATE[40001]: Serialization failure: 1213 Deadlock found when trying to get lock; try restarting transaction at /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:605)
[stacktrace]
#0 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(605): PDOStatement->execute()
#1 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(788): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback()
#3 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(593): Illuminate\\Database\\Connection->run()
#4 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(545): Illuminate\\Database\\Connection->affectingStatement()
#5 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3421): Illuminate\\Database\\Connection->update()
#6 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1057): Illuminate\\Database\\Query\\Builder->update()
#7 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(679): Illuminate\\Database\\Eloquent\\Builder->update()
#8 /www/wwwroot/admin.notopc.vip/app/Models/LeverTransaction.php(256): App\\Models\\LeverTransaction::checkNeedStopPriceTrade()
#9 /www/wwwroot/admin.notopc.vip/app/Jobs/LeverHandle.php(38): App\\Models\\LeverTransaction::tradeHandle()
#10 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\LeverHandle->handle()
#11 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#13 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#14 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#15 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()
#16 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()
#17 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()
#19 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow()
#20 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()
#21 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then()
#23 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()
#24 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()
#25 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#26 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()
#27 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()
#28 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()
#29 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#30 /www/wwwroot/admin.notopc.vip/vendor/laravel/horizon/src/Console/WorkCommand.php(51): Illuminate\\Queue\\Console\\WorkCommand->handle()
#31 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Horizon\\Console\\WorkCommand->handle()
#32 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#34 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#36 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#37 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#38 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()
#40 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()
#41 /www/wwwroot/admin.notopc.vip/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()
#42 /www/wwwroot/admin.notopc.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#43 /www/wwwroot/admin.notopc.vip/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 {main}
"}
