<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ElasticsearchHealthService
{
    const HEALTH_CHECK_KEY = 'elasticsearch_health_status';
    const HEALTH_CHECK_INTERVAL = 60; // 60秒检查一次
    const FAILURE_THRESHOLD = 5; // 连续失败5次后降级
    const FAILURE_COUNT_KEY = 'elasticsearch_failure_count';

    /**
     * 检查 Elasticsearch 是否健康
     */
    public static function isHealthy(): bool
    {
        // 从缓存获取健康状态
        $healthStatus = Cache::get(self::HEALTH_CHECK_KEY);
        
        if ($healthStatus === null) {
            // 首次检查或缓存过期，进行健康检查
            return self::performHealthCheck();
        }
        
        return $healthStatus === 'healthy';
    }

    /**
     * 执行健康检查
     */
    private static function performHealthCheck(): bool
    {
        try {
            $client = \App\Models\MarketHour::getEsearchClient();

            // ES 7.x 兼容的ping检查（不使用timeout参数）
            $result = $client->ping();

            if ($result) {
                // 重置失败计数
                Cache::forget(self::FAILURE_COUNT_KEY);
                Cache::put(self::HEALTH_CHECK_KEY, 'healthy', self::HEALTH_CHECK_INTERVAL);

                Log::info('Elasticsearch health check passed (ping successful)');
                return true;
            } else {
                self::recordFailure();
                return false;
            }

        } catch (\Exception $e) {
            self::recordFailure();

            Log::warning('Elasticsearch health check failed', [
                'error' => $e->getMessage(),
                'error_type' => get_class($e)
            ]);

            return false;
        }
    }

    /**
     * 记录失败次数
     */
    private static function recordFailure(): void
    {
        $failureCount = Cache::get(self::FAILURE_COUNT_KEY, 0) + 1;
        Cache::put(self::FAILURE_COUNT_KEY, $failureCount, 300); // 5分钟过期
        
        if ($failureCount >= self::FAILURE_THRESHOLD) {
            // 标记为不健康
            Cache::put(self::HEALTH_CHECK_KEY, 'unhealthy', self::HEALTH_CHECK_INTERVAL);
            
            Log::error('Elasticsearch marked as unhealthy', [
                'failure_count' => $failureCount,
                'threshold' => self::FAILURE_THRESHOLD
            ]);
        }
    }

    /**
     * 强制标记为不健康（用于紧急降级）
     */
    public static function markUnhealthy(): void
    {
        Cache::put(self::HEALTH_CHECK_KEY, 'unhealthy', self::HEALTH_CHECK_INTERVAL * 5);
        Log::warning('Elasticsearch manually marked as unhealthy');
    }

    /**
     * 强制标记为健康（用于恢复）
     */
    public static function markHealthy(): void
    {
        Cache::forget(self::FAILURE_COUNT_KEY);
        Cache::put(self::HEALTH_CHECK_KEY, 'healthy', self::HEALTH_CHECK_INTERVAL);
        Log::info('Elasticsearch manually marked as healthy');
    }

    /**
     * 获取当前状态信息
     */
    public static function getStatus(): array
    {
        return [
            'is_healthy' => self::isHealthy(),
            'failure_count' => Cache::get(self::FAILURE_COUNT_KEY, 0),
            'last_check' => Cache::get(self::HEALTH_CHECK_KEY . '_timestamp', 'never'),
            'threshold' => self::FAILURE_THRESHOLD
        ];
    }
}
