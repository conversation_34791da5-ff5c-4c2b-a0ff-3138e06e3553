{{-- Nova HEIC 图片显示支持 --}}
<script type="module">
// 导入 HEIC 转换模块
import { heicTo } from 'https://cdn.jsdelivr.net/npm/heic-to@1.2.1/dist/csp/heic-to.js';

// Nova 专用的 HEIC 支持
class NovaHeicSupport {
    constructor() {
        this.heicTo = heicTo;
        this.processedImages = new Set();
        this.init();
    }

    // 检查是否为 HEIC 文件
    isHeicFile(src) {
        if (!src) return false;
        try {
            const url = new URL(src, window.location.origin);
            const pathname = url.pathname.toLowerCase();
            return pathname.endsWith('.heic') || pathname.endsWith('.heif');
        } catch {
            return false;
        }
    }

    // 添加 Nova 专用样式
    addStyles() {
        if (document.getElementById('nova-heic-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'nova-heic-styles';
        style.textContent = `
            /* Nova HEIC 转换状态样式 */
            .nova-heic-loading {
                position: relative;
                background: #f8fafc url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><circle cx="25" cy="25" r="20" stroke="%236366f1" stroke-width="3" fill="none" stroke-dasharray="31.4 94.2"><animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="1s" values="0 25 25;360 25 25"></animateTransform></circle></svg>') no-repeat center;
                background-size: 30px;
                min-height: 60px;
                opacity: 0.9;
            }
            
            .nova-heic-converted {
                border: 2px solid #10b981 !important;
                border-radius: 6px;
                box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
                transition: all 0.2s ease;
            }
            
            .nova-heic-error {
                border: 2px dashed #ef4444 !important;
                border-radius: 6px;
                background: rgba(239, 68, 68, 0.05);
                position: relative;
            }
            
            .nova-heic-error::after {
                content: "HEIC 转换失败";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(239, 68, 68, 0.9);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 500;
                z-index: 10;
                white-space: nowrap;
            }
            
            .nova-heic-badge {
                position: absolute;
                top: 4px;
                right: 4px;
                background: #10b981;
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 9px;
                font-weight: 600;
                z-index: 10;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .nova-heic-converted:hover .nova-heic-badge {
                opacity: 1;
            }
            
            /* Nova 表格中的图片样式 */
            .nova-heic-converted.w-8.h-8 {
                border-width: 1px;
            }
            
            /* Nova 详情页图片样式 */
            .nova-heic-converted[style*="width: 800px"] {
                border-width: 3px;
            }
        `;
        document.head.appendChild(style);
    }

    // 转换单个 HEIC 图片
    async convertImage(img) {
        const src = img.src;
        
        // 避免重复处理
        if (this.processedImages.has(src)) return;
        this.processedImages.add(src);
        
        const originalAlt = img.alt || '';
        const originalTitle = img.title || '';
        
        try {
            console.log('Nova HEIC: 开始转换', src);
            
            // 添加加载状态
            img.classList.add('nova-heic-loading');
            
            // 获取 HEIC 文件
            const response = await fetch(src);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const blob = await response.blob();
            console.log('Nova HEIC: 文件下载完成', blob.size, 'bytes');
            
            // 转换为 JPEG
            const jpegBlob = await this.heicTo({
                blob: blob,
                type: "image/jpeg",
                quality: 0.9 // Nova 后台使用高质量
            });
            
            console.log('Nova HEIC: 转换完成', jpegBlob.size, 'bytes');
            
            // 创建对象 URL
            const jpegUrl = URL.createObjectURL(jpegBlob);
            
            // 设置加载完成回调
            img.onload = () => {
                URL.revokeObjectURL(jpegUrl);
                img.classList.remove('nova-heic-loading');
                img.classList.add('nova-heic-converted');
                
                // 添加转换标识
                const badge = document.createElement('span');
                badge.className = 'nova-heic-badge';
                badge.textContent = 'HEIC→JPG';
                
                // 确保父元素有相对定位
                const parent = img.parentElement;
                if (parent && getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                parent?.appendChild(badge);
                
                console.log('Nova HEIC: 显示成功');
            };
            
            img.onerror = () => {
                URL.revokeObjectURL(jpegUrl);
                img.classList.remove('nova-heic-loading');
                img.classList.add('nova-heic-error');
                console.error('Nova HEIC: 显示失败');
            };
            
            // 替换图片源
            img.src = jpegUrl;
            img.title = originalTitle + ' (HEIC→JPG)';
            
        } catch (error) {
            console.error('Nova HEIC: 转换失败', error);
            img.classList.remove('nova-heic-loading');
            img.classList.add('nova-heic-error');
            img.title = originalTitle + ` (转换失败: ${error.message})`;
        }
    }

    // 处理页面中的 HEIC 图片
    async processImages() {
        const images = document.querySelectorAll('img');
        const heicImages = Array.from(images).filter(img => 
            this.isHeicFile(img.src) && !this.processedImages.has(img.src)
        );
        
        if (heicImages.length === 0) return;
        
        console.log(`Nova HEIC: 找到 ${heicImages.length} 张 HEIC 图片`);
        
        // 并发转换
        const promises = heicImages.map(img => this.convertImage(img));
        await Promise.allSettled(promises);
    }

    // 监听 Nova 页面变化
    observeChanges() {
        // 监听 DOM 变化
        const observer = new MutationObserver(async (mutations) => {
            let hasNewImages = false;
            
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.tagName === 'IMG' && this.isHeicFile(node.src)) {
                            hasNewImages = true;
                        }
                        const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                        images.forEach(img => {
                            if (this.isHeicFile(img.src)) {
                                hasNewImages = true;
                            }
                        });
                    }
                });
            });
            
            if (hasNewImages) {
                await this.processImages();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 监听 Nova 路由变化
        let currentUrl = window.location.href;
        setInterval(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                console.log('Nova HEIC: 页面变化，重新处理图片');
                setTimeout(() => this.processImages(), 500);
            }
        }, 1000);
    }

    // 初始化
    async init() {
        console.log('Nova HEIC: 初始化支持');
        
        this.addStyles();
        await this.processImages();
        this.observeChanges();
        
        console.log('Nova HEIC: 初始化完成');
    }
}

// 启动 Nova HEIC 支持
document.addEventListener('DOMContentLoaded', () => {
    new NovaHeicSupport();
});

// 如果页面已加载完成
if (document.readyState !== 'loading') {
    new NovaHeicSupport();
}
</script>

<style>
/* 确保 Nova 图片容器支持相对定位 */
.nova-resource-table img,
.nova-detail-field img {
    position: relative;
}
</style>
