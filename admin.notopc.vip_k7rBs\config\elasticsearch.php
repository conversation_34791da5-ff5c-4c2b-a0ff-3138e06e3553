<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Elasticsearch Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Elasticsearch connections. Multiple
    | connections are supported.
    |
    */

    'connections' => [
        'default' => [
            'hosts' => [
                env('ELASTICSEARCH_HOST', 'http://127.0.0.1:9200'),
            ],
            'username' => env('ELASTICSEARCH_USERNAME'),
            'password' => env('ELASTICSEARCH_PASSWORD'),
            // 高频写入优化配置（每秒写入场景）
            'timeout' => env('ELASTICSEARCH_TIMEOUT', 10), // 写入需要更长超时
            'connect_timeout' => env('ELASTICSEARCH_CONNECT_TIMEOUT', 3), // 连接超时
            'retries' => env('ELASTICSEARCH_RETRIES', 2), // 写入重试2次
            'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', false), // 内网关闭SSL验证

            // 连接管理配置
            'connection_max_age' => env('ELASTICSEARCH_CONNECTION_MAX_AGE', 1800), // 连接最大生命周期30分钟
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the connections below you wish
    | to use as your default connection for all work.
    |
    */

    'default' => env('ELASTICSEARCH_CONNECTION', 'default'),

    /*
    |--------------------------------------------------------------------------
    | Index Settings
    |--------------------------------------------------------------------------
    |
    | Here you can configure default index settings
    |
    */

    'index' => [
        'market.kline' => [
            'number_of_shards' => 1,
            'number_of_replicas' => 0,
        ],
    ],
];
