<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HeicController extends Controller
{
    /**
     * 将存储中的图片（包含 HEIC/HEIF）转换为 JPEG 并输出，支持缩放。
     * 需要服务器安装 Imagick 并支持 HEIC，或已配置 GD/外部转换。
     */
    public function show(Request $request)
    {
        $disk = $request->query('disk', 'public');
        $pathEncoded = $request->query('path', '');
        $width = intval($request->query('w', 800));
        $path = base64_decode($pathEncoded);

        if (empty($path)) {
            abort(404);
        }

        // 归一化路径：/storage/xx 或 storage/xx 转为 public 磁盘相对路径
        $normalizedDisk = $disk;
        $normalizedPath = $path;
        if (strpos($normalizedPath, '/storage/') === 0) {
            $normalizedDisk = 'public';
            $normalizedPath = ltrim(substr($normalizedPath, 9), '/'); // 去掉 /storage/
        } elseif (strpos($normalizedPath, 'storage/') === 0) {
            $normalizedDisk = 'public';
            $normalizedPath = ltrim(substr($normalizedPath, 8), '/');
        }

        // 读取内容：优先 Storage，其次 public_path，最后远程 URL
        $content = null;
        if (Storage::disk($normalizedDisk)->exists($normalizedPath)) {
            $content = Storage::disk($normalizedDisk)->get($normalizedPath);
        } elseif (file_exists(public_path($normalizedPath))) {
            $content = file_get_contents(public_path($normalizedPath));
        } elseif (preg_match('#^https?://#i', $normalizedPath)) {
            $context = stream_context_create(['http' => ['timeout' => 10]]);
            $content = @file_get_contents($normalizedPath, false, $context);
        }

        if ($content === null) {
            \Log::warning('HEIC preview: file not found', ['disk' => $normalizedDisk, 'path' => $normalizedPath]);
            abort(404);
        }

        $ext = strtolower(pathinfo(parse_url($normalizedPath, PHP_URL_PATH) ?? '', PATHINFO_EXTENSION));

        // 按你的逻辑：仅对 heic/heif 用 Imagick 文件路径方式读取并转 PNG 输出
        if (in_array($ext, ['heic', 'heif'], true)) {
            if (!extension_loaded('imagick')) {
                \Log::warning('HEIC preview: imagick not loaded');
                return response('Imagick not available', 415);
            }
            try {
                // 写入临时 .heic 文件，再由 Imagick 读取（某些环境对 Blob 不识别 HEIC）
                $tmpIn = tempnam(sys_get_temp_dir(), 'heic_in_') . '.heic';
                file_put_contents($tmpIn, $content);

                $image = new \Imagick();
                $image->readImage($tmpIn);

                if ($width > 0) {
                    $image->thumbnailImage($width, 0);
                }

                $image->setImageFormat('png');
                $output = $image->getImageBlob();
                $image->clear();
                $image->destroy();
                @unlink($tmpIn);
                return response($output, 200, ['Content-Type' => 'image/png']);
            } catch (\Throwable $e) {
                \Log::warning('HEIC preview Imagick read failed', ['error' => $e->getMessage()]);
                // 退回 CLI 尝试
                $converted = $this->convertViaCli($content, $width);
                if ($converted !== null) {
                    return response($converted, 200, ['Content-Type' => 'image/jpeg']);
                }
            }
        }

        // 最终回退：原图直出（若浏览器不支持则下载）
        $mime = $this->guessMime($ext);
        return response($content, 200, ['Content-Type' => $mime]);
    }

    private function guessMime(string $ext): string
    {
        switch ($ext) {
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            case 'gif':
                return 'image/gif';
            case 'webp':
                return 'image/webp';
            case 'heic':
            case 'heif':
                return 'image/heic';
            default:
                return 'application/octet-stream';
        }
    }

    private function convertViaCli(string $heicContent, int $width = 800): ?string
    {
        try {
            $tmpIn = tempnam(sys_get_temp_dir(), 'heic_in_');
            $tmpOut = tempnam(sys_get_temp_dir(), 'heic_out_');
            // 确保后缀，部分工具依赖扩展名判断
            $inPath = $tmpIn . '.heic';
            $outPath = $tmpOut . '.jpg';
            file_put_contents($inPath, $heicContent);

            $w = max(1, $width);
            $cmds = [
                // ImageMagick 7
                "magick \"$inPath\" -thumbnail {$w}x \"$outPath\"",
                // ImageMagick 6
                "convert \"$inPath\" -thumbnail {$w}x \"$outPath\"",
                // libheif 工具
                "heif-convert \"$inPath\" \"$outPath\""
            ];

            foreach ($cmds as $cmd) {
                @shell_exec($cmd . ' 2>/dev/null');
                if (file_exists($outPath) && filesize($outPath) > 0) {
                    $data = file_get_contents($outPath);
                    @unlink($inPath);
                    @unlink($outPath);
                    return $data ?: null;
                }
            }

            @unlink($inPath);
            @unlink($outPath);
        } catch (\Throwable $e) {
            \Log::warning('HEIC CLI convert failed', ['error' => $e->getMessage()]);
        }
        return null;
    }
}


