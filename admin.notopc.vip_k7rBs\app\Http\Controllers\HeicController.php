<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HeicController extends Controller
{
    /**
     * 将存储中的图片（包含 HEIC/HEIF）转换为 JPEG 并输出，支持缩放。
     * 需要服务器安装 Imagick 并支持 HEIC，或已配置 GD/外部转换。
     */
    public function show(Request $request)
    {
        $disk = $request->query('disk', 'public');
        $pathEncoded = $request->query('path', '');
        $width = intval($request->query('w', 800));
        $path = base64_decode($pathEncoded);

        // 添加调试日志
        \Log::info('HEIC Controller - Request received', [
            'disk' => $disk,
            'path_encoded' => $pathEncoded,
            'path_decoded' => $path,
            'width' => $width
        ]);

        if (empty($path)) {
            \Log::warning('HEIC Controller - Empty path provided');
            abort(404);
        }

        // 归一化路径：/storage/xx 或 storage/xx 转为 public 磁盘相对路径
        $normalizedDisk = $disk;
        $normalizedPath = $path;
        if (strpos($normalizedPath, '/storage/') === 0) {
            $normalizedDisk = 'public';
            $normalizedPath = ltrim(substr($normalizedPath, 9), '/'); // 去掉 /storage/
        } elseif (strpos($normalizedPath, 'storage/') === 0) {
            $normalizedDisk = 'public';
            $normalizedPath = ltrim(substr($normalizedPath, 8), '/');
        }

        // 读取内容：优先 Storage，其次 public_path，最后远程 URL
        $content = null;
        if (Storage::disk($normalizedDisk)->exists($normalizedPath)) {
            $content = Storage::disk($normalizedDisk)->get($normalizedPath);
        } elseif (file_exists(public_path($normalizedPath))) {
            $content = file_get_contents(public_path($normalizedPath));
        } elseif (preg_match('#^https?://#i', $normalizedPath)) {
            $context = stream_context_create(['http' => ['timeout' => 10]]);
            $content = @file_get_contents($normalizedPath, false, $context);
        }

        if ($content === null) {
            \Log::warning('HEIC preview: file not found', ['disk' => $normalizedDisk, 'path' => $normalizedPath]);
            abort(404);
        }

        $ext = strtolower(pathinfo(parse_url($normalizedPath, PHP_URL_PATH) ?? '', PATHINFO_EXTENSION));

        // 按你的逻辑：仅对 heic/heif 用 Imagick 文件路径方式读取并转 PNG 输出
        if (in_array($ext, ['heic', 'heif'], true)) {
            \Log::info('HEIC Controller - Processing HEIC/HEIF file', [
                'extension' => $ext,
                'content_size' => strlen($content)
            ]);

            if (!extension_loaded('imagick')) {
                \Log::warning('HEIC preview: imagick not loaded');
                return response('Imagick not available', 415);
            }

            // 检查 ImageMagick 是否支持 HEIC
            try {
                $imagick = new \Imagick();
                $formats = $imagick->queryFormats();
                $heicSupported = in_array('HEIC', $formats) || in_array('HEIF', $formats);

                \Log::info('HEIC Controller - ImageMagick format support', [
                    'heic_supported' => $heicSupported,
                    'available_formats' => array_intersect($formats, ['HEIC', 'HEIF', 'JPEG', 'PNG'])
                ]);

                if (!$heicSupported) {
                    \Log::warning('HEIC Controller - ImageMagick does not support HEIC format');
                    // 直接尝试 CLI 转换
                    $converted = $this->convertViaCli($content, $width);
                    if ($converted !== null) {
                        return response($converted, 200, ['Content-Type' => 'image/jpeg']);
                    }
                    return response('HEIC format not supported', 415);
                }

            } catch (\Exception $e) {
                \Log::error('HEIC Controller - Error checking ImageMagick support', [
                    'error' => $e->getMessage()
                ]);
            }

            try {
                // 写入临时 .heic 文件，再由 Imagick 读取（某些环境对 Blob 不识别 HEIC）
                $tmpIn = tempnam(sys_get_temp_dir(), 'heic_in_') . '.heic';
                $bytesWritten = file_put_contents($tmpIn, $content);

                \Log::info('HEIC Controller - Temporary file created', [
                    'temp_file' => $tmpIn,
                    'bytes_written' => $bytesWritten,
                    'file_exists' => file_exists($tmpIn)
                ]);

                $image = new \Imagick();
                $image->readImage($tmpIn);

                // 获取原始图片信息
                $originalWidth = $image->getImageWidth();
                $originalHeight = $image->getImageHeight();

                \Log::info('HEIC Controller - Image loaded successfully', [
                    'original_width' => $originalWidth,
                    'original_height' => $originalHeight,
                    'requested_width' => $width
                ]);

                if ($width > 0 && $width < $originalWidth) {
                    $image->thumbnailImage($width, 0);
                    \Log::info('HEIC Controller - Image resized', [
                        'new_width' => $image->getImageWidth(),
                        'new_height' => $image->getImageHeight()
                    ]);
                }

                $image->setImageFormat('png');
                $image->setImageCompressionQuality(85);
                $output = $image->getImageBlob();

                \Log::info('HEIC Controller - Conversion successful', [
                    'output_size' => strlen($output)
                ]);

                $image->clear();
                $image->destroy();
                @unlink($tmpIn);

                return response($output, 200, [
                    'Content-Type' => 'image/png',
                    'Cache-Control' => 'public, max-age=31536000'
                ]);

            } catch (\Throwable $e) {
                \Log::error('HEIC preview Imagick read failed', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'temp_file' => $tmpIn ?? 'not_created'
                ]);

                // 清理临时文件
                if (isset($tmpIn) && file_exists($tmpIn)) {
                    @unlink($tmpIn);
                }

                // 退回 CLI 尝试
                \Log::info('HEIC Controller - Falling back to CLI conversion');
                $converted = $this->convertViaCli($content, $width);
                if ($converted !== null) {
                    return response($converted, 200, ['Content-Type' => 'image/jpeg']);
                }

                // 最终失败
                return response('HEIC conversion failed: ' . $e->getMessage(), 500);
            }
        }

        // 最终回退：原图直出（若浏览器不支持则下载）
        $mime = $this->guessMime($ext);
        return response($content, 200, ['Content-Type' => $mime]);
    }

    private function guessMime(string $ext): string
    {
        switch ($ext) {
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            case 'gif':
                return 'image/gif';
            case 'webp':
                return 'image/webp';
            case 'heic':
            case 'heif':
                return 'image/heic';
            default:
                return 'application/octet-stream';
        }
    }

    private function convertViaCli(string $heicContent, int $width = 800): ?string
    {
        try {
            \Log::info('HEIC Controller - Starting CLI conversion', [
                'content_size' => strlen($heicContent),
                'width' => $width
            ]);

            $tmpIn = tempnam(sys_get_temp_dir(), 'heic_in_');
            $tmpOut = tempnam(sys_get_temp_dir(), 'heic_out_');
            // 确保后缀，部分工具依赖扩展名判断
            $inPath = $tmpIn . '.heic';
            $outPath = $tmpOut . '.jpg';

            $bytesWritten = file_put_contents($inPath, $heicContent);
            \Log::info('HEIC Controller - CLI temp files created', [
                'input_file' => $inPath,
                'output_file' => $outPath,
                'bytes_written' => $bytesWritten
            ]);

            $w = max(1, $width);
            $cmds = [
                // ImageMagick 7
                "magick \"$inPath\" -thumbnail {$w}x \"$outPath\" 2>&1",
                // ImageMagick 6
                "convert \"$inPath\" -thumbnail {$w}x \"$outPath\" 2>&1",
                // libheif 工具
                "heif-convert \"$inPath\" \"$outPath\" 2>&1"
            ];

            foreach ($cmds as $i => $cmd) {
                \Log::info('HEIC Controller - Trying CLI command', [
                    'command_index' => $i,
                    'command' => $cmd
                ]);

                $output = shell_exec($cmd);

                \Log::info('HEIC Controller - CLI command result', [
                    'command_index' => $i,
                    'output_file_exists' => file_exists($outPath),
                    'output_file_size' => file_exists($outPath) ? filesize($outPath) : 0,
                    'shell_output' => $output
                ]);

                if (file_exists($outPath) && filesize($outPath) > 0) {
                    $data = file_get_contents($outPath);
                    @unlink($inPath);
                    @unlink($outPath);

                    \Log::info('HEIC Controller - CLI conversion successful', [
                        'command_used' => $cmd,
                        'output_size' => strlen($data)
                    ]);

                    return $data ?: null;
                }
            }

            \Log::warning('HEIC Controller - All CLI commands failed');
            @unlink($inPath);
            @unlink($outPath);

        } catch (\Throwable $e) {
            \Log::error('HEIC CLI convert failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return null;
    }
}


