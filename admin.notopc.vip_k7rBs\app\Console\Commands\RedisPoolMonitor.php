<?php

namespace App\Console\Commands;

use App\Services\RedisService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class RedisPoolMonitor extends Command
{
    protected $signature = 'redis:pool {action=status : status|clean|close}';
    protected $description = 'Redis连接池监控和管理';

    public function handle()
    {
        $action = $this->argument('action');

        switch($action) {
            case 'status':
                $this->showPoolStatus();
                break;
            case 'clean':
                $this->cleanExpiredConnections();
                break;
            case 'close':
                $this->closeAllConnections();
                break;
            default:
                $this->error('无效的操作。可用操作: status, clean, close');
        }
    }

    protected function showPoolStatus()
    {
        $this->info('Redis连接池状态:');
        $this->line('==================');

        // 显示自定义RedisService连接池状态
        $poolStatus = RedisService::getPoolStatus();
        
        if(empty($poolStatus)) {
            $this->info('自定义RedisService: 无活动连接');
        } else {
            foreach($poolStatus as $db => $status) {
                $this->line("数据库 {$db}:");
                $this->line("  总连接数: {$status['connection_count']}");
                $this->line("  活动连接数: {$status['active_connections']}");
                $this->line("  过期连接数: {$status['expired_connections']}");
                $this->line('');
            }
        }

        // 尝试获取Redis服务器信息 - 重新实现
        try {
            // 直接使用redis-cli命令获取信息
            $output = shell_exec('redis-cli info clients 2>/dev/null');
            
            if($output) {
                $this->line('Redis服务器客户端信息:');
                $lines = explode("\n", trim($output));
                foreach($lines as $line) {
                    $line = trim($line);
                    if(!empty($line) && strpos($line, ':') !== false) {
                        $this->line("  " . $line);
                    }
                }
            } else {
                // 备用方案：使用Laravel Redis
                $redis = Redis::connection();
                $connectedClients = $redis->eval("return redis.call('info', 'clients')", 0);
                
                if(is_string($connectedClients)) {
                    $this->line('Redis服务器客户端信息:');
                    $lines = explode("\r\n", $connectedClients);
                    foreach($lines as $line) {
                        if(!empty($line) && strpos($line, ':') !== false) {
                            $this->line("  " . $line);
                        }
                    }
                } else {
                    $this->line('Redis服务器客户端信息: 无法获取详细信息');
                }
            }
        } catch(\Exception $e) {
            $this->warn('无法获取Redis服务器信息，尝试基本连接测试...');
            try {
                $redis = Redis::connection();
                $result = $redis->ping();
                $this->line("Redis连接测试: " . ($result ? "成功" : "失败"));
            } catch(\Exception $e2) {
                $this->error('Redis连接完全失败: ' . $e2->getMessage());
            }
        }
    }

    protected function cleanExpiredConnections()
    {
        $this->info('清理过期的Redis连接...');
        
        // 这会触发RedisService的自动清理
        RedisService::getInstance(1);
        
        $this->info('清理完成！');
        $this->showPoolStatus();
    }

    protected function closeAllConnections()
    {
        $this->info('关闭所有Redis连接...');
        
        RedisService::closeConnections();
        
        $this->info('所有连接已关闭！');
        $this->showPoolStatus();
    }
} 