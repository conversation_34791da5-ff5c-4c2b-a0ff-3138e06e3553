<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\LeverTransaction;

class LeverHandle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $params;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job - 优化版本
     *
     * @return void
     */
    public function handle()
    {
        // 检查队列是否被暂停
        if (\Illuminate\Support\Facades\Redis::get('lever_queue_paused')) {
            \Log::info('LeverHandle job paused - queue is temporarily disabled');
            $this->release(60); // 1分钟后重试
            return;
        }

        extract($this->params);

        $startTime = microtime(true);

        try {
            // 价格大于0才做更新处理
            if (bc_comp($now_price, '0') > 0) {
                LeverTransaction::tradeHandle($legal_id, $currency_id, $now_price, $now);

                $executionTime = microtime(true) - $startTime;

                // 记录慢查询（超过1秒）
                if ($executionTime > 1.0) {
                    \Log::warning('LeverHandle job slow execution', [
                        'legal_id' => $legal_id,
                        'currency_id' => $currency_id,
                        'execution_time' => $executionTime,
                        'threshold' => 1.0
                    ]);
                }

            } else {
                \Log::warning('LeverHandle job: 价格异常', [
                    'legal_id' => $legal_id,
                    'currency_id' => $currency_id,
                    'price' => $now_price
                ]);
            }

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;

            // 检查是否是死锁错误 - 检查多个层级的错误码
            $isDeadlock = false;

            // 检查 PDO 错误码 40001
            if ($e instanceof \Illuminate\Database\QueryException && $e->getCode() == 40001) {
                $isDeadlock = true;
            }

            // 检查错误信息中的关键字
            if (strpos($e->getMessage(), '1213') !== false ||
                strpos($e->getMessage(), 'Deadlock') !== false ||
                strpos($e->getMessage(), '40001') !== false) {
                $isDeadlock = true;
            }

            if ($isDeadlock) {
                \Log::error('LeverHandle deadlock detected', [
                    'legal_id' => $legal_id ?? null,
                    'currency_id' => $currency_id ?? null,
                    'price' => $now_price ?? null,
                    'error' => $e->getMessage(),
                    'execution_time' => $executionTime,
                    'attempts' => $this->attempts()
                ]);

                // 死锁时重新入队，使用指数退避
                $maxRetries = 3; // 最大重试3次
                if ($this->attempts() < $maxRetries) {
                    $delay = 100000; // 100ms 基础延迟
                    $delay *= pow(2, $this->attempts() - 1); // 指数退避

                    \Log::info('LeverHandle retrying after deadlock', [
                        'legal_id' => $legal_id ?? null,
                        'currency_id' => $currency_id ?? null,
                        'attempt' => $this->attempts(),
                        'delay_microseconds' => $delay
                    ]);

                    usleep($delay);
                    $this->release($delay / 1000000); // 转换为秒
                    return;
                }
            }

            \Log::error('LeverHandle job failed', [
                'legal_id' => $legal_id ?? null,
                'currency_id' => $currency_id ?? null,
                'price' => $now_price ?? null,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'execution_time' => $executionTime,
                'attempts' => $this->attempts()
            ]);

            // 重新抛出异常以便队列系统处理重试
            throw $e;
        }
    }
}
