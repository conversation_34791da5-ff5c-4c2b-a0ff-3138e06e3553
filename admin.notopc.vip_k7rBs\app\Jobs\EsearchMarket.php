<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\MarketHour;

class EsearchMarket implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $marketData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($market_data)
    {
        $this->marketData = $market_data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // 激进增加延迟处理，大幅减少 ES 压力
            usleep(500000); // 增加到0.5秒延迟

            // 检查 ES 健康状态，如果不健康则跳过
            if (!\App\Services\ElasticsearchHealthService::isHealthy()) {
                \Log::warning('ES not healthy, skipping market data write', [
                    'symbol' => $this->marketData['base-currency'] ?? 'unknown',
                    'period' => $this->marketData['period'] ?? 'unknown'
                ]);
                return;
            }

            MarketHour::getAndSetEsearchMarket($this->marketData);

        } catch (\Exception $e) {
            \Log::error('EsearchMarket job failed', [
                'market_data' => $this->marketData,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // 如果 ES 写入失败，不要让任务失败，避免重试造成更大压力
            // throw $e; // 注释掉，避免任务重试
        }
    }
}
