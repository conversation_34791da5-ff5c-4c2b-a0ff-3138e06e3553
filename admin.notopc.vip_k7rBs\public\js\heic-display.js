/**
 * Nova HEIC 图片前端显示支持
 * 自动检测并转换 HEIC 格式图片为 JPEG 在浏览器中显示
 * 专为 Laravel Nova 后台优化
 */

console.log('Nova HEIC Display: 脚本加载中...');

// 动态导入 heic-to 模块
let heicToModule = null;

async function loadHeicModule() {
    if (heicToModule) return heicToModule;
    
    try {
        // 使用 CDN 加载 heic-to 模块
        const module = await import('https://cdn.jsdelivr.net/npm/heic-to@1.2.1/dist/csp/heic-to.js');
        heicToModule = module.heicTo;
        console.log('HEIC 转换模块加载成功');
        return heicToModule;
    } catch (error) {
        console.error('HEIC 转换模块加载失败:', error);
        return null;
    }
}

// 检查是否为 HEIC 文件
function isHeicFile(src) {
    if (!src) return false;
    const url = new URL(src, window.location.origin);
    const pathname = url.pathname.toLowerCase();
    return pathname.endsWith('.heic') || pathname.endsWith('.heif');
}

// 添加转换状态样式
function addHeicStyles() {
    if (document.getElementById('heic-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'heic-styles';
    style.textContent = `
        .heic-loading {
            position: relative;
            min-height: 100px;
            background: #f8f9fa url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" stroke="%233498db" stroke-width="8" fill="none" stroke-dasharray="62.8 188.8"><animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="1s" values="0 50 50;360 50 50" keyTimes="0;1"></animateTransform></circle></svg>') no-repeat center;
            background-size: 40px;
            opacity: 0.8;
        }
        
        .heic-converted {
            border: 2px solid #10b981 !important;
            border-radius: 6px;
            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
            transition: all 0.2s ease;
        }

        /* Nova 专用样式 */
        .nova-resource-table .heic-converted {
            border-width: 1px;
        }

        .nova-detail-field .heic-converted {
            border-width: 3px;
        }
        
        .heic-error {
            border: 2px dashed #dc3545 !important;
            border-radius: 4px;
            position: relative;
        }
        
        .heic-error::after {
            content: "HEIC 转换失败";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10;
        }
        
        .heic-tooltip {
            position: relative;
        }
        
        .heic-tooltip::before {
            content: "HEIC → JPG";
            position: absolute;
            top: -25px;
            right: 5px;
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .heic-tooltip:hover::before {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}

// 转换单个 HEIC 图片
async function convertHeicImage(img, heicTo) {
    const src = img.src;
    const originalAlt = img.alt || '';
    const originalTitle = img.title || '';
    
    try {
        console.log('开始转换 HEIC 图片:', src);
        
        // 添加加载状态
        img.classList.add('heic-loading');
        img.alt = 'HEIC 图片转换中...';
        
        // 获取 HEIC 文件
        const response = await fetch(src);
        if (!response.ok) {
            throw new Error(`HTTP 错误! 状态码: ${response.status}`);
        }
        
        const blob = await response.blob();
        console.log('HEIC 文件下载完成，大小:', blob.size, 'bytes');
        
        // 转换为 JPEG
        const jpegBlob = await heicTo({
            blob: blob,
            type: "image/jpeg",
            quality: 0.85 // 高质量
        });
        
        console.log('HEIC 转换完成，JPEG 大小:', jpegBlob.size, 'bytes');
        
        // 创建对象 URL 并替换
        const jpegUrl = URL.createObjectURL(jpegBlob);
        
        img.onload = function() {
            // 转换成功后的处理
            URL.revokeObjectURL(jpegUrl); // 释放内存
            img.classList.remove('heic-loading');
            img.classList.add('heic-converted', 'heic-tooltip');
            console.log('HEIC 图片显示成功:', src);
        };
        
        img.onerror = function() {
            // 加载失败处理
            URL.revokeObjectURL(jpegUrl);
            img.classList.remove('heic-loading');
            img.classList.add('heic-error');
            img.alt = originalAlt + ' [显示失败]';
            console.error('转换后的 JPEG 图片加载失败');
        };
        
        img.src = jpegUrl;
        img.alt = originalAlt;
        img.title = originalTitle + ' (已从 HEIC 转换)';
        
    } catch (error) {
        console.error('HEIC 转换失败:', error);
        img.classList.remove('heic-loading');
        img.classList.add('heic-error');
        img.alt = originalAlt + ' [HEIC 转换失败]';
        img.title = originalTitle + ' (HEIC 转换失败: ' + error.message + ')';
    }
}

// 处理页面中的所有 HEIC 图片
async function processHeicImages() {
    // 检查浏览器支持
    if (!window.fetch || !window.URL || !window.Blob) {
        console.warn('浏览器不支持 HEIC 转换所需 API');
        return;
    }
    
    // 加载转换模块
    const heicTo = await loadHeicModule();
    if (!heicTo) {
        console.error('无法加载 HEIC 转换模块');
        return;
    }
    
    // 添加样式
    addHeicStyles();
    
    // 查找所有图片
    const allImages = document.querySelectorAll('img');
    const heicImages = Array.from(allImages).filter(img => isHeicFile(img.src));
    
    if (heicImages.length === 0) {
        console.log('页面中没有找到 HEIC 图片');
        return;
    }
    
    console.log(`找到 ${heicImages.length} 张 HEIC 图片，开始转换...`);
    
    // 并发转换所有 HEIC 图片
    const promises = heicImages.map(img => convertHeicImage(img, heicTo));
    
    try {
        await Promise.allSettled(promises);
        console.log('所有 HEIC 图片处理完成');
    } catch (error) {
        console.error('批量转换 HEIC 图片时出错:', error);
    }
}

// 监听新添加的图片（适用于动态加载的内容）
function observeNewImages() {
    const observer = new MutationObserver(async (mutations) => {
        let hasNewImages = false;
        
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // 检查新添加的图片
                    if (node.tagName === 'IMG' && isHeicFile(node.src)) {
                        hasNewImages = true;
                    }
                    // 检查新添加元素内的图片
                    const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                    images.forEach(img => {
                        if (isHeicFile(img.src)) {
                            hasNewImages = true;
                        }
                    });
                }
            });
        });
        
        if (hasNewImages) {
            console.log('检测到新的 HEIC 图片，开始处理...');
            await processHeicImages();
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('HEIC 图片监听器已启动');
}

// 初始化
async function initHeicSupport() {
    console.log('初始化 HEIC 显示支持...');
    
    // 处理现有图片
    await processHeicImages();
    
    // 监听新图片
    observeNewImages();
    
    console.log('HEIC 显示支持初始化完成');
}

// 导出主要函数
window.HeicDisplay = {
    init: initHeicSupport,
    process: processHeicImages,
    convert: convertHeicImage
};

// 自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initHeicSupport);
} else {
    initHeicSupport();
}
