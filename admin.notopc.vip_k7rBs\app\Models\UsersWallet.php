<?php

namespace App\Models;

use App\Models\Currency;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UsersWallet extends Model
{

    protected $table = "users_wallet";
    public $timestamps = false;
    protected $hidden = ["private"];
    protected $appends = ["account","currency_name", "currency_type", "is_legal", "is_lever", "is_match", "is_micro", "cny_price", "usdt_price","balance","lock_balance"];//, "pb_price"


    public function getCreateTimeAttribute()
    {
        $value = $this->attributes['create_time'];
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    public function getAccountAttribute()
    {
        return $this->belongsTo(Users::class,  'user_id', 'id')->value('email');
    }

    public function getCurrencyTypeAttribute()
    {
        return $this->hasOne(Currency::class, 'id', 'currency')->value('type');
    }


    public function getCurrencyNameAttribute()
    {
        return $this->currencyCoin()->value('name');
    }

    public function getIsLegalAttribute()
    {
        return $this->currencyCoin()->value('is_legal');
    }

    public function getIsLeverAttribute()
    {
        return $this->currencyCoin()->value('is_lever');
    }

    public function getIsMatchAttribute()
    {
        return $this->currencyCoin()->value('is_match');
    }

    public function getIsMicroAttribute()
    {
        return $this->currencyCoin()->value('is_micro');
    }

    public function currencyCoin()
    {
        return $this->belongsTo(Currency::class, 'currency', 'id');
    }



    public static function makeWallet($user_id)
    {
        try {
//            \Log::info('UsersWallet::makeWallet - Starting wallet creation', [
//                'user_id' => $user_id
//            ]);

            $currency = Currency::all();
            $created_count = 0;
            $existing_count = 0;

            foreach ($currency as $key => $value) {
                $res = self::where([
                     'currency' => $value->id,
                    'user_id' => $user_id
                    ])->first();

                if(!$res){
                    self::insert([
                        'currency' => $value->id,
                        'user_id' => $user_id,
                        'address' => null,
                        'change_balance' => 0,
                        'lever_balance' => 0,
                        'micro_balance' => 0,
                        'lock_change_balance' => 0,
                        'lock_lever_balance' => 0,
                        'lock_micro_balance' => 0,
                        'create_time' => time()
                    ]);
                    $created_count++;

                    \Log::info('UsersWallet::makeWallet - Created wallet', [
                        'user_id' => $user_id,
                        'currency_id' => $value->id,
                        'currency_name' => $value->name ?? 'unknown'
                    ]);
                } else {
                    $existing_count++;
                }
            }

//            \Log::info('UsersWallet::makeWallet - Wallet creation completed', [
//                'user_id' => $user_id,
//                'total_currencies' => $currency->count(),
//                'created_wallets' => $created_count,
//                'existing_wallets' => $existing_count
//            ]);

            return true;

        } catch (\Exception $e) {
            \Log::error('UsersWallet::makeWallet - Error during wallet creation', [
                'user_id' => $user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    public static function makeWalletSimulation($user_id)
    {
        $currency = Currency::all();

        foreach ($currency as $key => $value) {
            $res = self::where([
                'currency' => $value->id,
                'user_id' => $user_id
            ])->first();
            if(!$res){
                self::insert([
                    'currency' => $value->id,
                    'user_id' => $user_id,
                    'address' => null,
                    'change_balance' => 50000,
                    'create_time' => time()
                ]);
            }

        }
        return true;
    }

    public static function getAddress(UsersWallet $wallet){
        $saveFlag = false;

        if($wallet->currency == 3){

            $return = ['omni'=>$wallet->address_2,'erc20' => $wallet->address];
        }else{


            $return = $wallet->address;
        }

        return $return;
    }


    public static function getUsdtWallet($userId){
       return  self::where("user_id", $userId)
            ->where("currency", 1)
            ->first();
    }

    public static function getDF1Wallet($userId){
        $res =   self::where("user_id", $userId)
            ->where("currency", 29)
            ->first();
        return $res;
    }


    public function getUsdtPriceAttribute()
    {
        return $this->currencyCoin()->value('price') ?? 1;
    }
    public function getBalanceAttribute()
    {
        $userId = $this->user()->value('id');
        if (empty($userId)) {
            return '0';
        }
        $wal = UsersWallet::where('currency', $this->attributes['currency'])
            ->where('user_id', $userId)
            ->first();

        if (!$wal) {
            return '0';
        }

        $lever = (float) ($wal->lever_balance ?? 0);
        $change = (float) ($wal->change_balance ?? 0);
        $micro = (float) ($wal->micro_balance ?? 0);
        return (string) ($lever + $change + $micro);
    }

    public function getLockBalanceAttribute()
    {
        $userId = $this->user()->value('id');
        if (empty($userId)) {
            return '0';
        }
        $wal = UsersWallet::where('currency', $this->attributes['currency'])
            ->where('user_id', $userId)
            ->first();

        if (!$wal) {
            return '0';
        }

        $lockLever = (float) ($wal->lock_lever_balance ?? 0);
        $lockChange = (float) ($wal->lock_change_balance ?? 0);
        $lockMicro = (float) ($wal->lock_micro_balance ?? 0);
        return (string) ($lockLever + $lockChange + $lockMicro);
    }
    public function getCnyPriceAttribute()
    {
        $currency_id = $this->attributes['currency'];
        return Currency::getCnyPrice($currency_id);
    }

    public function user()
    {
        return $this->belongsTo(Users::class, 'user_id', 'id');
    }

    public function getPrivateAttribute($value)
    {

        return empty($value) ? '' : decrypt($value);
    }
    public function setPrivateAttribute($value)
    {
        $this->attributes['private'] = encrypt($value);
    }

    public function getAccountNumberAttribute($value)
    {
        return $this->user()->value('account_number') ?? '';
    }
}
