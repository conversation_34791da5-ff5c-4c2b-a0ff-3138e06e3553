<?php
/**
 * HEIC 支持检测脚本
 * 用于检查服务器是否支持 HEIC 图片处理
 */

echo "🔍 HEIC 支持检测\n";
echo "================\n\n";

// 1. 检查 PHP 扩展
echo "📊 1. PHP 扩展检查\n";
echo "-------------------\n";

$extensions = ['imagick', 'gd'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "- {$ext}: " . ($loaded ? '✅ 已安装' : '❌ 未安装') . "\n";
    
    if ($loaded && $ext === 'imagick') {
        try {
            $imagick = new Imagick();
            $version = $imagick->getVersion();
            echo "  版本信息: " . ($version['versionString'] ?? 'Unknown') . "\n";
        } catch (Exception $e) {
            echo "  版本检查失败: " . $e->getMessage() . "\n";
        }
    }
}
echo "\n";

// 2. 检查 ImageMagick 支持的格式
echo "🎨 2. ImageMagick 格式支持\n";
echo "-------------------------\n";

if (extension_loaded('imagick')) {
    try {
        $imagick = new Imagick();
        $formats = $imagick->queryFormats();
        
        $imageFormats = ['HEIC', 'HEIF', 'JPEG', 'PNG', 'GIF', 'WEBP', 'BMP', 'TIFF'];
        $supportedFormats = [];
        $unsupportedFormats = [];
        
        foreach ($imageFormats as $format) {
            if (in_array($format, $formats)) {
                $supportedFormats[] = $format;
                echo "- {$format}: ✅ 支持\n";
            } else {
                $unsupportedFormats[] = $format;
                echo "- {$format}: ❌ 不支持\n";
            }
        }
        
        echo "\n支持的格式总数: " . count($supportedFormats) . "\n";
        echo "不支持的格式: " . implode(', ', $unsupportedFormats) . "\n";
        
        // 特别检查 HEIC 支持
        $heicSupported = in_array('HEIC', $formats) || in_array('HEIF', $formats);
        echo "\n🎯 HEIC/HEIF 支持状态: " . ($heicSupported ? '✅ 支持' : '❌ 不支持') . "\n";
        
    } catch (Exception $e) {
        echo "❌ ImageMagick 格式检查失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ ImageMagick 扩展未安装，无法检查格式支持\n";
}
echo "\n";

// 3. 检查命令行工具
echo "⚡ 3. 命令行工具检查\n";
echo "-------------------\n";

$commands = [
    'magick' => 'magick -version 2>&1',
    'convert' => 'convert -version 2>&1',
    'heif-convert' => 'heif-convert --version 2>&1',
    'ffmpeg' => 'ffmpeg -version 2>&1'
];

foreach ($commands as $tool => $cmd) {
    $output = shell_exec($cmd);
    if ($output && !empty(trim($output))) {
        echo "- {$tool}: ✅ 可用\n";
        // 显示版本信息的第一行
        $lines = explode("\n", trim($output));
        if (!empty($lines[0])) {
            echo "  " . substr($lines[0], 0, 80) . "\n";
        }
    } else {
        echo "- {$tool}: ❌ 不可用\n";
    }
}
echo "\n";

// 4. 测试 HEIC 转换（如果有测试文件）
echo "🧪 4. HEIC 转换测试\n";
echo "-------------------\n";

// 创建一个简单的测试图片（PNG）来模拟转换过程
if (extension_loaded('imagick')) {
    try {
        echo "创建测试图片...\n";
        
        // 创建一个简单的测试图片
        $testImage = new Imagick();
        $testImage->newImage(100, 100, new ImagickPixel('red'));
        $testImage->setImageFormat('png');
        
        // 测试各种格式转换
        $formats = ['jpeg', 'png', 'webp'];
        foreach ($formats as $format) {
            try {
                $testImage->setImageFormat($format);
                $blob = $testImage->getImageBlob();
                echo "- 转换到 {$format}: ✅ 成功 (" . strlen($blob) . " bytes)\n";
            } catch (Exception $e) {
                echo "- 转换到 {$format}: ❌ 失败 - " . $e->getMessage() . "\n";
            }
        }
        
        $testImage->clear();
        $testImage->destroy();
        
    } catch (Exception $e) {
        echo "❌ 测试图片创建失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ ImageMagick 未安装，跳过转换测试\n";
}
echo "\n";

// 5. 系统信息
echo "💻 5. 系统信息\n";
echo "-------------\n";
echo "- PHP 版本: " . PHP_VERSION . "\n";
echo "- 操作系统: " . PHP_OS . "\n";
echo "- 临时目录: " . sys_get_temp_dir() . "\n";
echo "- 临时目录可写: " . (is_writable(sys_get_temp_dir()) ? '✅ 是' : '❌ 否') . "\n";
echo "- 内存限制: " . ini_get('memory_limit') . "\n";
echo "- 最大执行时间: " . ini_get('max_execution_time') . "s\n";
echo "\n";

// 6. 建议和解决方案
echo "💡 6. 建议和解决方案\n";
echo "===================\n";

if (!extension_loaded('imagick')) {
    echo "❌ ImageMagick 扩展未安装\n";
    echo "解决方案:\n";
    echo "1. Ubuntu/Debian: sudo apt-get install php-imagick\n";
    echo "2. CentOS/RHEL: sudo yum install php-imagick\n";
    echo "3. 编译安装: pecl install imagick\n\n";
}

if (extension_loaded('imagick')) {
    $imagick = new Imagick();
    $formats = $imagick->queryFormats();
    $heicSupported = in_array('HEIC', $formats) || in_array('HEIF', $formats);
    
    if (!$heicSupported) {
        echo "❌ ImageMagick 不支持 HEIC 格式\n";
        echo "解决方案:\n";
        echo "1. 安装 libheif 库:\n";
        echo "   Ubuntu/Debian: sudo apt-get install libheif-dev\n";
        echo "   CentOS/RHEL: sudo yum install libheif-devel\n";
        echo "2. 重新编译 ImageMagick 并启用 HEIC 支持\n";
        echo "3. 或者使用命令行工具 heif-convert\n\n";
    } else {
        echo "✅ ImageMagick 支持 HEIC 格式\n";
        echo "建议:\n";
        echo "1. 确保服务器有足够的内存处理大图片\n";
        echo "2. 考虑添加图片缓存机制\n";
        echo "3. 监控转换性能和错误日志\n\n";
    }
}

// 7. 测试 URL
echo "🔗 7. 测试 URL\n";
echo "-------------\n";
echo "如果 HEIC 支持正常，可以使用以下 URL 测试:\n";
echo "http://your-domain.com/heic/show?path=" . base64_encode('path/to/your/image.heic') . "&w=800\n";
echo "\n";

echo "📝 8. 日志检查\n";
echo "-------------\n";
echo "如果遇到问题，请检查以下日志:\n";
echo "1. Laravel 日志: storage/logs/laravel.log\n";
echo "2. PHP 错误日志: " . (ini_get('error_log') ?: '/var/log/php_errors.log') . "\n";
echo "3. Web 服务器日志 (Nginx/Apache)\n";
echo "\n";

echo "✅ 检测完成！\n";
echo "如果 HEIC 不支持，请按照上述建议进行配置。\n";
?>
