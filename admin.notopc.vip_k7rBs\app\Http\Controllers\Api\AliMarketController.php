<?php

    namespace App\Http\Controllers\Api;

    use App\Jobs\HandleMicroTrade;
    use App\Jobs\LeverUpdate;
    use App\Jobs\SendLever;
    use App\Models\Currency;
    use App\Models\MarketKine;
    use App\Models\CurrencyOpening;
    use App\Models\CurrencyQuotation;
    use App\Jobs\EsearchMarket;
    use App\Jobs\SendMarket;
    use App\Jobs\UpdateCurrencyPrice;
    use App\Logic\MicroTradeLogic;
    use App\Models\LeverTransaction;
    use App\Models\MarketHour;
    use App\Models\UserChat;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\DB;

    defined('ACCOUNT_ID') || define('ACCOUNT_ID', '********');
    defined('ACCESS_KEY') || define('ACCESS_KEY', 'c96392eb-b7c57373-f646c2ef-25a14');
    defined('SECRET_KEY') || define('SECRET_KEY', '');

    class AliMarketController
    {

        protected $code = '99aa7fc8390140fab4b0f244fa93c368';
        protected $lastRequestTime;

        protected $signature = "get_kline_data";
        protected $description = "获取K线图数据";
        private $url = "https://api.huobi.br.com";
        private $api = "";
        public $api_method = "";
        public $req_method = "";

        /**
         * 行情查询
         */
        public function marketInfo($symbol)
        {
            $symbol = strtoupper($symbol);
            $querys = "symbol=" . $symbol . "&withks=1&withticks=0";//USDCNH
            $data = $this->curl("/query/com", $querys);
            return json_decode($data, true)['Obj'];
        }

        /**
         * 交易查询
         * @param $symbol
         * @return mixed
         */
        public function ticks($symbol)
        {
            $symbol = strtoupper($symbol);
            $querys = "symbol=" . $symbol . "&count=50";//USDCNH
            $data = $this->curl("/query/ticks", $querys);
            return json_decode($data, true)['Obj'];
        }

        public function historyD(Request $request)
        {
            $symbol = $request->input('symbol');
            $date = $request->input('date');
            $symbol = strtoupper($symbol);
            $query = "date=" . $date . "&period=D&symbol=" . $symbol . "&withlast=0";
            $data = $this->curl("/query/comkm4v2", $query);
            $history = explode(';', json_decode($data, true)['Obj']);
            foreach ($history as $value) {
                $info = explode(',', $value);
                $time = $this->formatTimeline(4, $info[0]);//
                $data = [
                    'id' => $time,
                    'period' => "1day",//
                    'base-currency' => $symbol,
                    'quote-currency' => 'USDT',
                    'open' => $info[2],
                    'close' => $info[1],
                    'high' => $info[3],
                    'low' => $info[4],
                    'vol' => $info[5],
                    'amount' => $info[6],
                ];
                MarketHour::setEsearchMarket($data);
            }
            return 'ok';
        }

        public function historyW(Request $request)
        {
            $symbol = $request->input('symbol');
            $date = $request->input('date');
            $symbol = strtoupper($symbol);
            $query = "date=" . $date . "&period=W&symbol=" . $symbol . "&withlast=0";
            $data = $this->curl("/query/comkm4v2", $query);
            $history = explode(';', json_decode($data, true)['Obj']);
            foreach ($history as $value) {
                $info = explode(',', $value);
                $time = $this->formatTimeline(8, $info[0]);//
                $data = [
                    'id' => $time,
                    'period' => "1week",//
                    'base-currency' => $symbol,
                    'quote-currency' => 'USDT',
                    'open' => $info[2],
                    'close' => $info[1],
                    'high' => $info[3],
                    'low' => $info[4],
                    'vol' => $info[5],
                    'amount' => $info[6],
                ];
                MarketHour::setEsearchMarket($data);
            }
            return 'ok';
        }

        public function historyM(Request $request)
        {
            $symbol = $request->input('symbol');
            $date = $request->input('date');
            $symbol = strtoupper($symbol);
            $query = "date=" . $date . "&period=M&symbol=" . $symbol . "&withlast=0";
            $data = $this->curl("/query/comkm4v2", $query);
            $history = explode(';', json_decode($data, true)['Obj']);
            foreach ($history as $value) {
                $info = explode(',', $value);
                $time = $this->formatTimeline(9, $info[0]);
                $data = [
                    'id' => $time,
                    'period' => "1mon",
                    'base-currency' => $symbol,
                    'quote-currency' => 'USDT',
                    'open' => $info[2],
                    'close' => $info[1],
                    'high' => $info[3],
                    'low' => $info[4],
                    'vol' => $info[5],
                    'amount' => $info[6],
                ];
                MarketHour::setEsearchMarket($data);
            }
            return 'ok';
        }

        /*public function getAliInfo()
        {
            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);
            foreach ($all as $i => $item) {
                if ($this->getOpening($item->id) == 0) {
                    continue;
                }
                $symbol = strtoupper($item->name);
                $querys = "symbol=" . $symbol . "&withks=1&withticks=0";//USDCNH
                $data = $this->curl("/query/com", $querys);
                $data = json_decode($data, true);

                if (!$data) {
                    continue;
                }
                if ($data['Code'] != '0') {
                    continue;
                }
                $ali_market_data = $data['Obj'];
                $price = explode(',', $ali_market_data['M1']);
                if ($item->floating != 0) {
                    $price[1] += $item->floating;
                    $ali_market_data['P'] += $item->floating;
                    $price[2] += $item->floating;
                    $price[3] += $item->floating;
                }
                $timeType = ['1min' => 5, '5min' => 6, '15min' => 1, '30min' => 7, '60min' => 2, '1day' => 4, '1week' => 8, '1mon' => 9];
                $periods = ['1min', '5min', '15min', '30min', '60min', '1day', '1mon', '1week'];
                foreach ($periods as $value) {
                    $time = $this->formatTimeline($timeType[$value], $ali_market_data['Tick']);
                    $market_data = [
                        'id' => $time,
                        'period' => $value,
                        'base-currency' => $item->name,
                        'quote-currency' => 'USDT',
                        'open' => sctonum($price[1]),
                        'close' => sctonum($ali_market_data['P']),
                        'high' => sctonum($price[2]),
                        'low' => sctonum($price[3]),
                        'vol' => sctonum($ali_market_data['V']),
                        'amount' => sctonum($ali_market_data['NV']),
                    ];

                    $kline_data = [
                        'type' => 'kline',
                        'period' => $value,
                        'match_id' => $item->match_id,
                        'currency_id' => $item->id,
                        'currency_name' => $item->name,
                        'legal_id' => 1,
                        'legal_name' => 'USDT',
                        'open' => sctonum($price[1]),
                        'close' => sctonum($ali_market_data['P']),
                        'high' => sctonum($price[2]),
                        'low' => sctonum($price[3]),
                        'symbol' => $item->name . '/' . 'USDT',
                        'volume' => sctonum($ali_market_data['NV']),
                        'time' => $time,
                    ];

                    if ($value == '1min') {
                        //处理期权
                        HandleMicroTrade::dispatch($kline_data)->onQueue('micro_trade:handle');
                    }
                    if ($value == '1day') {
                        //推送币种的日行情(带涨副)
                        $change = $this->calcIncreasePair($kline_data);
                        bc_comp($change, 0) > 0 && $change = '+' . $change;
                        //追加涨副等信息
                        $daymarket_data = [
                            'type' => 'daymarket',
                            'change' => $change,
                            'now_price' => $market_data['close'],
                            'api_form' => 'huobi_websocket',
                        ];
                        $kline_data = array_merge($kline_data, $daymarket_data);
                        //存入数据库
                        CurrencyQuotation::getInstance(1, $item->id)
                            ->updateData([
                                'change' => $daymarket_data['change'],
                                'now_price' => $kline_data['close'],
                                'volume' => $kline_data['volume'],
                            ]);
                        $now = microtime(true);
                        $params = [
                            'legal_id' => $kline_data['legal_id'],
                            'legal_name' => $kline_data['legal_name'],
                            'currency_id' => $kline_data['currency_id'],
                            'currency_name' => $kline_data['currency_name'],
                            'now_price' => $kline_data['close'],
                            'now' => $now
                        ];
                        //价格大于0才进行任务推送
                        if (bc_comp($kline_data['close'], 0) > 0) {
                            LeverUpdate::dispatch($params)->onQueue('lever:update');
                        }
                    }
                    SendMarket::dispatch($kline_data)->onQueue('kline.all');
                    EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列

                }
            }
        }*/

        public function getOpening($id): int
        {
            $opening = CurrencyOpening::with([])->where('currency_id', $id)->first();
            if ($opening) {
                $time = date('H:i:s', time());
                switch (date('w', time())) {
                    case 1:
                        if ($time > $opening->mon_begin && $time < $opening->mon_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    case 2:
                        if ($time > $opening->tue_begin && $time < $opening->tue_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    case 3:
                        if ($time > $opening->wed_begin && $time < $opening->wed_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    case 4:
                        if ($time > $opening->thu_begin && $time < $opening->thu_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    case 5:
                        if ($time > $opening->fin_begin && $time < $opening->fin_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    case 6:
                        if ($time > $opening->sat_begin && $time < $opening->sat_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    case 7:
                        if ($time > $opening->sun_begin && $time < $opening->sun_end) {
                            return 1;
                        } else {
                            return 0;
                        }
                    default:
                        return 1;
                }
            } else {
                return 1;
            }
        }


        public function indexTest()
        {
            $es_client = MarketHour::getEsearchClient();
            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);


            foreach ($all as $v) {
                $params = [
                    'index' => 'market.kline',
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['match' => ['period' => '60min']],
                                    ['match' => ['base-currency' => $v->name]],
                                    ['match' => ['quote-currency' => 'USDT']],
                                ]
                                // 'filter' => [
                                //     'range' => [
                                //         'id' => [
                                //             'gte' => '1722519681',
                                //             'lte' => '1723297281',
                                //         ],
                                //     ],
                                // ],
                            ]
                        ],
                        'sort' => [
                            'id' => [
                                'order' => 'asc',
                            ],
                        ],
                        'size' => '10000',
                    ]
                ];


                // $result = $es_client->search($params);
                // if (isset($result['hits'])) {
                //     $data = array_column($result['hits']['hits'], '_source');
                // } else {
                //     $data = [];
                // }
                // var_dump($data);
                // return;

                $es_client->deleteByQuery($params);
            }

            // foreach ($data as $v){
            //     $basecurrency = 'USDCAD';
            //     $querycurrency = 'USDT';
            //     $v['base-currency'] = $basecurrency;
            //     $v['quote-currency'] = $querycurrency;
            //     $type = $basecurrency . '.' . $querycurrency . '.' . $v['period'];
            //     $params2 = [
            //         'index' => 'market.kline',
            //         'type' => '_doc',
            //         'id' => $type . '.' . $v['id'],
            //         'body' => $v,
            //     ];

            //     $es_client->index($params2);
            // }
            return;
            //     echo "开始推送\r\n";
            //     $all = DB::table('currency')->where('is_display', '1')->get();
            //     $all_arr = $this->object2array($all);
            //     $legal = DB::table('currency')->where('is_display', '1')->where('is_legal', '1')->get();
            //     $legal_arr = $this->object2array($legal);
            //     $ar = [];
            //     foreach ($legal_arr as $legal) {
            //         foreach ($all_arr as $item) {
            //             if ($legal['id'] != $item['id']) {
            //                 echo "begin2";
            //                 $ar_a = [];
            //                 $ar_a['name'] = strtolower($item['name']) . strtolower($legal['name']);
            //                 $ar_a['currency_id'] = $item['id'];
            //                 $ar_a['legal_id'] = $legal['id'];
            //                 $ar[] = $ar_a;
            //             }
            //         }
            //     }
            //     echo "开始遍历币种\r\n";
            //     foreach ($ar as $vv) {
            //         if (in_array($vv["name"], array("btcusdt", "ethusdt", "ltcusdt", "bchusdt", "eosusdt"))) {
            //             $ar_new[] = $vv;
            //         }
            //     }
            //     file_put_contents("ar_new.txt", json_encode($ar_new) . PHP_EOL, FILE_APPEND);
            //     foreach ($ar_new as $it) {
            //         echo "遍历币种开始\r\n";
            //         $data = array();
            //         echo "开始请求\r\n";
            //         $data = $this->get_history_kline($it['name'], '1min', 1);
            //         dd($data);
            //   }

        }

        public function initTest1()
        {
            $period = '1min';

            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);
            // 初始化一个空数组来存储所有的name值
            $names = [];

            // 遍历$people数组
            foreach ($all as $person) {
                // 将每个person的name值添加到$names数组中
                $names[] = $person->name;
            }

            // 使用implode()函数将$names数组中的所有元素用逗号连接起来
            $namesString = implode(',', $names);
            $datak = $this->testCurl("/exchange_pluralK.action", $namesString, '1', '202408091300');

            $datak = explode(PHP_EOL, $datak);

            $rq = $datak[1];
            unset($datak[0]);
            unset($datak[1]);
            unset($datak[2]);
            $datak_arr = array();
            foreach ($datak as $v) {
                array_push($datak_arr, explode(",", $v));
            }

            foreach ($all as $i => $item) {
                foreach ($datak_arr as $data) {
                    // 假设的时间字符串
                    // 原始时间字符串
                    if ($item->name != $data[0]) {
                        continue;
                    }

                    $dateTimeStr = $rq;


                    // 将时间字符串分割成年、月、日、时、分
                    $year = substr($dateTimeStr, 0, 4);
                    $month = substr($dateTimeStr, 4, 2);
                    $day = substr($dateTimeStr, 6, 2);

                    if ($period == '1day') {
                        // 构造一个PHP能理解的日期时间字符串
                        $dateTimeStrFormatted = "$year-$month-$day";
                    } else {
                        // 构造一个PHP能理解的日期时间字符串
                        $dateTimeStrFormatted = "$year-$month-$day " . $data[1];
                    }

                    // 使用strtotime函数将字符串转换为时间戳
                    $time = strtotime($dateTimeStrFormatted);

                    $market_data = [
                        'id' => $time,
                        'period' => $period,
                        'base-currency' => $item->name,
                        'quote-currency' => 'USDT',
                        'open' => sctonum($data[2]),
                        'close' => sctonum($data[5]),
                        'high' => sctonum($data[3]),
                        'low' => sctonum($data[4]),
                        'vol' => sctonum(0),
                        'amount' => sctonum($data[2]),
                    ];

                    $kline_data = [
                        'type' => 'kline',
                        'period' => $period,
                        'match_id' => $item->match_id,
                        'currency_id' => $item->id,
                        'currency_name' => $item->name,
                        'legal_id' => 1,
                        'legal_name' => 'USDT',
                        'open' => sctonum($data[2]),
                        'close' => sctonum($data[5]),
                        'high' => sctonum($data[3]),
                        'low' => sctonum($data[4]),
                        'symbol' => $item->name . '/' . 'USDT',
                        'volume' => 0,
                        'time' => $time * 1000,
                    ];
                    SendMarket::dispatch($kline_data)->onQueue('kline.all');
                    EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列
                }

            }


        }

        public function initTest()
        {
            $period = '30min';

            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);
            // 初始化一个空数组来存储所有的name值
            $names = [];

            // 遍历$people数组
            foreach ($all as $person) {
                // 将每个person的name值添加到$names数组中
                $names[] = $person->name;
            }

            // 使用implode()函数将$names数组中的所有元素用逗号连接起来
            $namesString = implode(',', $names);
            $datak = $this->testCurl("/exchange_pluralK.action", $namesString, '30');
            $datak = explode(PHP_EOL, $datak);

//  $rq = $datak[1];
            unset($datak[0]);
            // unset($datak[1]);
            // unset($datak[2]);
            $datak_arr = array();
            foreach ($datak as $v) {
                array_push($datak_arr, explode(",", $v));
            }

            foreach ($all as $i => $item) {
                foreach ($datak_arr as $data) {
                    // 假设的时间字符串
                    // 原始时间字符串
                    if ($item->name != $data[0]) {
                        continue;
                    }
                    $rq = explode(" ", $data[1]);
                    $dateTimeStr = $rq[0];


                    // 将时间字符串分割成年、月、日、时、分
                    $year = substr($dateTimeStr, 0, 4);
                    $month = substr($dateTimeStr, 4, 2);
                    $day = substr($dateTimeStr, 6, 2);

                    if ($period == '1day') {
                        // 构造一个PHP能理解的日期时间字符串
                        $dateTimeStrFormatted = "$year-$month-$day";
                    } else {
                        // 构造一个PHP能理解的日期时间字符串
                        $dateTimeStrFormatted = "$year-$month-$day " . $rq[1];
                    }


                    // 使用strtotime函数将字符串转换为时间戳
                    $time = strtotime($dateTimeStrFormatted);

                    $market_data = [
                        'id' => $time,
                        'period' => $period,
                        'base-currency' => $item->name,
                        'quote-currency' => 'USDT',
                        'open' => sctonum($data[2]),
                        'close' => sctonum($data[5]),
                        'high' => sctonum($data[3]),
                        'low' => sctonum($data[4]),
                        'vol' => sctonum(0),
                        'amount' => sctonum($data[2]),
                    ];

                    $kline_data = [
                        'type' => 'kline',
                        'period' => $period,
                        'match_id' => $item->match_id,
                        'currency_id' => $item->id,
                        'currency_name' => $item->name,
                        'legal_id' => 1,
                        'legal_name' => 'USDT',
                        'open' => sctonum($data[2]),
                        'close' => sctonum($data[5]),
                        'high' => sctonum($data[3]),
                        'low' => sctonum($data[4]),
                        'symbol' => $item->name . '/' . 'USDT',
                        'volume' => 0,
                        'time' => $time * 1000,
                    ];


                    SendMarket::dispatch($kline_data)->onQueue('kline.all');
                    EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列
                }

            }


        }


        public function getTest30()
        {
            $utcTime = gmdate('Y-m-d H:i:s');
// 如果你想检查这个UTC时间是否是周末，你可以这样做：
            $utcTimestamp = strtotime($utcTime); // 通常这一步是多余的，因为gmdate()已经返回了UTC时间
            $utcDayOfWeek = date('N', $utcTimestamp); // 但这里我们使用date()和UTC时间戳来检查星期几
            if ($utcDayOfWeek == 6 || $utcDayOfWeek == 7) {
                echo "今天是周六或周日。";
                return;
            }
            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);


            foreach ($all as $i => $item) {
                try {
                    $datak = $this->curl_k($item->name, 4, 1);
                } catch (\Throwable $e) {


                }


                // var_dump($datak);
                // return;
                $data_json = json_decode($datak);
                $data2 = $data_json->data->kline_list;
                foreach ($data2 as $data) {
                    $time = $data->timestamp;
                    $open_price = $data->open_price;
                    $close_price = $data->close_price;
                    $high_price = $data->high_price;
                    $low_price = $data->low_price;
                    $volume = $data->volume;
                    $symbol = strtoupper($item->name);


                    if (!$data) {

                        continue;
                    }


                    $timeType = ['1min' => 5, '5min' => 6, '15min' => 1, '30min' => 7, '60min' => 2, '1day' => 4, '1week' => 8, '1mon' => 9];
                    $periods = ['30min'];


                    foreach ($periods as $value) {

                        // $time = $this->formatTimeline($timeType[$value], $time);
                        // var_dump($res);

                        $market_data = [
                            'id' => $time,
                            'period' => $value,
                            'base-currency' => $item->name,
                            'quote-currency' => 'USDT',
                            'open' => sctonum($open_price),
                            'close' => sctonum($close_price),
                            'high' => sctonum($high_price),
                            'low' => sctonum($low_price),
                            'vol' => sctonum($volume),
                            'amount' => sctonum($close_price),
                        ];
                        EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列

                    }
                }


                // return;

            }
        }

        public function getTest15()
        {
            $utcTime = gmdate('Y-m-d H:i:s');
// 如果你想检查这个UTC时间是否是周末，你可以这样做：
            $utcTimestamp = strtotime($utcTime); // 通常这一步是多余的，因为gmdate()已经返回了UTC时间
            $utcDayOfWeek = date('N', $utcTimestamp); // 但这里我们使用date()和UTC时间戳来检查星期几
            if ($utcDayOfWeek == 6 || $utcDayOfWeek == 7) {
                echo "今天是周六或周日。";
                return;
            }
            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);


            foreach ($all as $i => $item) {
                try {
                    $datak = $this->curl_k($item->name, 3, 1);
                } catch (\Throwable $e) {


                }


                // var_dump($datak);
                // return;
                $data_json = json_decode($datak);
                $data2 = $data_json->data->kline_list;
                foreach ($data2 as $data) {
                    $time = $data->timestamp;
                    $open_price = $data->open_price;
                    $close_price = $data->close_price;
                    $high_price = $data->high_price;
                    $low_price = $data->low_price;
                    $volume = $data->volume;
                    $symbol = strtoupper($item->name);


                    if (!$data) {

                        continue;
                    }


                    $timeType = ['1min' => 5, '5min' => 6, '15min' => 1, '30min' => 7, '60min' => 2, '1day' => 4, '1week' => 8, '1mon' => 9];
                    $periods = ['15min'];


                    foreach ($periods as $value) {


                        // var_dump($res);

                        $market_data = [
                            'id' => $time,
                            'period' => $value,
                            'base-currency' => $item->name,
                            'quote-currency' => 'USDT',
                            'open' => sctonum($open_price),
                            'close' => sctonum($close_price),
                            'high' => sctonum($high_price),
                            'low' => sctonum($low_price),
                            'vol' => sctonum($volume),
                            'amount' => sctonum($close_price),
                        ];
                        EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列

                    }
                }


                // return;

            }
        }

        public function getTest()
        {

            $utcTime = gmdate('Y-m-d H:i:s');
// 如果你想检查这个UTC时间是否是周末，你可以这样做：
            $utcTimestamp = strtotime($utcTime); // 通常这一步是多余的，因为gmdate()已经返回了UTC时间
            $utcDayOfWeek = date('N', $utcTimestamp); // 但这里我们使用date()和UTC时间戳来检查星期几
            /*  if ($utcDayOfWeek == 6 || $utcDayOfWeek == 7) {
                  echo "今天是周六或周日。";
                  return;
              }*/

            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);
            foreach ($all as $i => $item) {
                try {
                    $datak = $this->curl_k($item->name, 1);
                   //$datak2 = $this->curl_k($item->name, 8);//每天
                } catch (\Throwable $e) {
                    continue;

                } finally {


                }


                $data_json = json_decode($datak);
                $data = $data_json->data->kline_list[0];

                $time = $data->timestamp;
                $open_price = $data->open_price;
                $close_price = $data->close_price;
                $high_price = $data->high_price;
                $low_price = $data->low_price;
                $volume = $data->volume;
                $symbol = strtoupper($item->name);
                if ($item->floating != 0) {
                    $open_price += $item->floating;
                    $close_price += $item->floating;
                    $high_price += $item->floating;
                    $low_price += $item->floating;
                }

                if (!$data) {

                    continue;
                }


                $timeType = ['1min' => 5, '5min' => 6, '15min' => 1, '30min' => 7, '60min' => 2, '1day' => 4, '1week' => 8, '1mon' => 9];
                $periods = ['1min', '15min', '30min', '60min'];


                foreach ($periods as $value) {

                    $time = $this->formatTimeline($timeType[$value], $time);
                    $pri = CurrencyQuotation::getInstance(1, $item->id);

                    // if (isset($pri)) {


                    //     if(bc_comp($pri['now_price'], $data2[1]) == 0){
                    //         var_dump("价格一样不更新".$value);
                    //         continue;
                    //     }

                    //     var_dump("1===".$pri['now_price']);
                    //     var_dump("2===".$data2[1]);
                    // }

                    $result = MarketHour::getEsearchMarketById(
                        $item->name,
                        'USDT',
                        $value,
                        $time
                    );


                    if (isset($result['_source'])) {

                        $origin_data = $result['_source'];

                        bc_comp($high_price, $origin_data['high']) < 0
                        && $high_price = $origin_data['high']; //新过来的价格如果不高于原最高价则不更新
                        bc_comp($low_price, $origin_data['low']) > 0
                        && $low_price = $origin_data['low']; //新过来的价格如果不低于原最低价则不更新
                    }

                    $market_data = [
                        'id' => $time,
                        'period' => $value,
                        'base-currency' => $item->name,
                        'quote-currency' => 'USDT',
                        'open' => sctonum($open_price),
                        'close' => sctonum($close_price),
                        'high' => sctonum($high_price),
                        'low' => sctonum($low_price),
                        'vol' => sctonum($volume),
                        'amount' => sctonum($close_price),
                    ];

                    $kline_data = [
                        'type' => 'kline',
                        'period' => $value,
                        'match_id' => $item->match_id,
                        'currency_id' => $item->id,
                        'currency_name' => $item->name,
                        'legal_id' => 1,
                        'legal_name' => 'USDT',
                        'open' => sctonum($open_price),
                        'close' => sctonum($close_price),
                        'high' => sctonum($high_price),
                        'low' => sctonum($low_price),
                        'symbol' => $item->name . '/' . 'USDT',
                        'volume' => $volume,
                        'now_price' => $market_data['close'],
                        'time' => $time * 1000,
                    ];

                    if ($value == '1min') {
                        //处理期权
                        HandleMicroTrade::dispatch($kline_data)->onQueue('micro_trade:handle');
                        //更新币种价格
                        UpdateCurrencyPrice::dispatch($kline_data)->onQueue('update_currency_price');

                    }

                    if ($value == '1day') {

                        //推送币种的日行情(带涨副)
                        $change = $this->calcIncreasePair($kline_data);
                        //dd($change);
                        bc_comp($change, 0) > 0 && $change = '+' . $change;
                        //追加涨副等信息
                        $daymarket_data = [
                            'type' => 'daymarket',
                            'change' => $change,
                            'now_price' => $market_data['close'],
                            'api_form' => 'huobi_websocket',
                        ];
                        $kline_data = array_merge($kline_data, $daymarket_data);
                        //存入数据库
                        CurrencyQuotation::getInstance(1, $item->id)
                            ->updateData([
                                'change' => $daymarket_data['change'],
                                'now_price' => $kline_data['close'],
                                'volume' => $kline_data['volume'],
                            ]);
                        $now = microtime(true);
                        $params = [
                            'legal_id' => $kline_data['legal_id'],
                            'legal_name' => $kline_data['legal_name'],
                            'currency_id' => $kline_data['currency_id'],
                            'currency_name' => $kline_data['currency_name'],
                            'now_price' => $kline_data['close'],
                            'now' => $now
                        ];
                        //价格大于0才进行任务推送
                        if (bc_comp($kline_data['close'], 0) > 0) {
                            LeverUpdate::dispatch($params)->onQueue('lever:update');
                        }
                    }
                    var_dump("更新" . $item->name . "时间:" . $time);
                    SendMarket::dispatch($kline_data)->onQueue('kline.all');
                    EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列


                }
                // return;
            }


        }
        public function getTest1Day()
        {

            $utcTime = gmdate('Y-m-d H:i:s');
// 如果你想检查这个UTC时间是否是周末，你可以这样做：
            $utcTimestamp = strtotime($utcTime); // 通常这一步是多余的，因为gmdate()已经返回了UTC时间
            $utcDayOfWeek = date('N', $utcTimestamp); // 但这里我们使用date()和UTC时间戳来检查星期几
            /*  if ($utcDayOfWeek == 6 || $utcDayOfWeek == 7) {
                  echo "今天是周六或周日。";
                  return;
              }*/


            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);
            foreach ($all as $i => $item) {
                try {
                    $datak = $this->curl_k($item->name, 8);
                    //$datak2 = $this->curl_k($item->name, 8);//每天
                } catch (\Throwable $e) {
                    continue;

                } finally {


                }


                $data_json = json_decode($datak);
                $data = $data_json->data->kline_list[0];

                $time = $data->timestamp;
                $open_price = $data->open_price;
                $close_price = $data->close_price;
                $high_price = $data->high_price;
                $low_price = $data->low_price;
                $volume = $data->volume;
                $symbol = strtoupper($item->name);
                if ($item->floating != 0) {
                    $open_price += $item->floating;
                    $close_price += $item->floating;
                    $high_price += $item->floating;
                    $low_price += $item->floating;
                }

                if (!$data) {

                    continue;
                }


                $timeType = ['1day' => 4,];
                $periods = ['1day'];


                foreach ($periods as $value) {

                    $time = $this->formatTimeline($timeType[$value], $time);
                    $pri = CurrencyQuotation::getInstance(1, $item->id);


                    $result = MarketHour::getEsearchMarketById(
                        $item->name,
                        'USDT',
                        $value,
                        $time
                    );

                    if (isset($result['_source'])) {

                        $origin_data = $result['_source'];

                        bc_comp($high_price, $origin_data['high']) < 0
                        && $high_price = $origin_data['high']; //新过来的价格如果不高于原最高价则不更新
                        bc_comp($low_price, $origin_data['low']) > 0
                        && $low_price = $origin_data['low']; //新过来的价格如果不低于原最低价则不更新
                    }

                    $market_data = [
                        'id' => $time,
                        'period' => $value,
                        'base-currency' => $item->name,
                        'quote-currency' => 'USDT',
                        'open' => sctonum($open_price),
                        'close' => sctonum($close_price),
                        'high' => sctonum($high_price),
                        'low' => sctonum($low_price),
                        'vol' => sctonum($volume),
                        'amount' => sctonum($close_price),
                    ];

                    $kline_data = [
                        'type' => 'kline',
                        'period' => $value,
                        'match_id' => $item->match_id,
                        'currency_id' => $item->id,
                        'currency_name' => $item->name,
                        'legal_id' => 1,
                        'legal_name' => 'USDT',
                        'open' => sctonum($open_price),
                        'close' => sctonum($close_price),
                        'high' => sctonum($high_price),
                        'low' => sctonum($low_price),
                        'symbol' => $item->name . '/' . 'USDT',
                        'volume' => $volume,
                        'time' => $time * 1000,
                    ];


                    if ($value == '1day') {

                        //推送币种的日行情(带涨副)
                        $change = $this->calcIncreasePair($kline_data);
                        //dd($change);
                        bc_comp($change, 0) > 0 && $change = '+' . $change;
                        //追加涨副等信息
                        $daymarket_data = [
                            'type' => 'daymarket',
                            'change' => $change,
                            'now_price' => $market_data['close'],
                            'api_form' => 'huobi_websocket',
                        ];
                        $kline_data = array_merge($kline_data, $daymarket_data);
                        //存入数据库
                        CurrencyQuotation::getInstance(1, $item->id)
                            ->updateData([
                                'change' => $daymarket_data['change'],
                                'now_price' => $kline_data['close'],
                                'volume' => $kline_data['volume'],
                            ]);
                        $now = microtime(true);
                        $params = [
                            'legal_id' => $kline_data['legal_id'],
                            'legal_name' => $kline_data['legal_name'],
                            'currency_id' => $kline_data['currency_id'],
                            'currency_name' => $kline_data['currency_name'],
                            'now_price' => $kline_data['close'],
                            'now' => $now
                        ];
                        //价格大于0才进行任务推送
                        if (bc_comp($kline_data['close'], 0) > 0) {
                            LeverUpdate::dispatch($params)->onQueue('lever:update');
                        }
                    }
                    var_dump("更新" . $item->name . "时间:" . $time);
                    SendMarket::dispatch($kline_data)->onQueue('kline.all');
                    EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列

                }
                // return;
            }


        }

        /**
         * 获取指定日期的1day数据
         * @param Request $request 包含 date 参数，格式：Y-m-d 例如 2024-01-15
         * @return string
         */
        public function getTestSpecifyDay(Request $request)
        {
            // 获取指定日期参数
            $date = $request->input('date');

            if (empty($date)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '请提供日期参数，格式：Y-m-d，例如：2024-01-15'
                ]);
            }

            // 验证日期格式
            $dateTime = \DateTime::createFromFormat('Y-m-d', $date);
            if (!$dateTime || $dateTime->format('Y-m-d') !== $date) {
                return response()->json([
                    'status' => 'error',
                    'message' => '日期格式错误，请使用 Y-m-d 格式，例如：2024-01-15'
                ]);
            }

            // 检查是否为未来日期
            $currentDate = date('Y-m-d');
            if ($date > $currentDate) {
                return response()->json([
                    'status' => 'error',
                    'message' => "不能查询未来日期的数据。当前日期: {$currentDate}，查询日期: {$date}"
                ]);
            }

            // 将指定日期转换为时间戳（当天结束时间）
            $specifyTimestamp = strtotime($date . ' 23:59:59');

            echo "开始获取指定日期 {$date} 的1day数据...\n";
            echo "指定时间戳: {$specifyTimestamp}\n";
            echo "API限流处理: 已启用请求延迟和重试机制\n";
            echo "================================\n";

            $all = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')->where('currency_matches.id', '<>', '')->get(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);

            $totalCurrencies = count($all);
            $processedCount = 0;
            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            echo "总币种数量: {$totalCurrencies}\n";
            echo "预计执行时间: " . round($totalCurrencies * 0.6) . "秒 (每币种约0.6秒)\n\n";

            // 分批处理，每批3个币种，减少并发压力
            $batchSize = 3;
            $batches = array_chunk($all->toArray(), $batchSize);
            $batchCount = count($batches);

            echo "分批处理: {$batchCount}批次，每批{$batchSize}个币种\n\n";

            foreach ($batches as $batchIndex => $batch) {
                $currentBatch = $batchIndex + 1;
                echo "--- 批次 {$currentBatch}/{$batchCount} ---\n";

                foreach ($batch as $item) {
                    $item = (object) $item; // 转换为对象
                $processedCount++;
                echo "({$processedCount}/{$totalCurrencies}) 处理币种: {$item->name}...\n";

                try {
                    // 使用带重试机制的方法获取指定日期的1day数据
                    $datak = $this->curl_k($item->name, 8, 1, $specifyTimestamp);

                    if ($datak === false) {
                        $errorCount++;
                        $errors[] = "币种 {$item->name}: API请求失败，跳过";
                        echo "  ❌ API请求失败，跳过\n";
                        continue;
                    }
                } catch (\Throwable $e) {
                    $errorCount++;
                    $errors[] = "币种 {$item->name}: " . $e->getMessage();
                    echo "  ❌ 错误: " . $e->getMessage() . "\n";
                    continue;
                }

                $data_json = json_decode($datak);

                if (!isset($data_json->data->kline_list) || empty($data_json->data->kline_list)) {
                    $errorCount++;
                    $errors[] = "币种 {$item->name}: 无法获取指定日期的数据";
                    echo "  ❌ 无法获取数据\n";
                    continue;
                }

                $data = $data_json->data->kline_list[0];

                $time = $data->timestamp;
                $open_price = $data->open_price;
                $close_price = $data->close_price;
                $high_price = $data->high_price;
                $low_price = $data->low_price;
                $volume = $data->volume;
                $symbol = strtoupper($item->name);

                // 应用浮动价格调整
                if ($item->floating != 0) {
                    $open_price += $item->floating;
                    $close_price += $item->floating;
                    $high_price += $item->floating;
                    $low_price += $item->floating;
                }

                if (!$data) {
                    continue;
                }

                $timeType = ['1day' => 4,];
                $periods = ['1day'];

                foreach ($periods as $value) {
                    $time = $this->formatTimeline($timeType[$value], $time);

                    // 检查ES中是否已存在该数据，如果存在则更新最高价和最低价
                    $result = MarketHour::getEsearchMarketById(
                        $item->name,
                        'USDT',
                        $value,
                        $time
                    );

                    if (isset($result['_source'])) {
                        $origin_data = $result['_source'];

                        bc_comp($high_price, $origin_data['high']) < 0
                        && $high_price = $origin_data['high']; //新过来的价格如果不高于原最高价则不更新
                        bc_comp($low_price, $origin_data['low']) > 0
                        && $low_price = $origin_data['low']; //新过来的价格如果不低于原最低价则不更新
                    }

                    $market_data = [
                        'id' => $time,
                        'period' => $value,
                        'base-currency' => $item->name,
                        'quote-currency' => 'USDT',
                        'open' => sctonum($open_price),
                        'close' => sctonum($close_price),
                        'high' => sctonum($high_price),
                        'low' => sctonum($low_price),
                        'vol' => sctonum($volume),
                        'amount' => sctonum($close_price),
                    ];

                    $kline_data = [
                        'type' => 'kline',
                        'period' => $value,
                        'match_id' => $item->match_id,
                        'currency_id' => $item->id,
                        'currency_name' => $item->name,
                        'legal_id' => 1,
                        'legal_name' => 'USDT',
                        'open' => sctonum($open_price),
                        'close' => sctonum($close_price),
                        'high' => sctonum($high_price),
                        'low' => sctonum($low_price),
                        'symbol' => $item->name . '/' . 'USDT',
                        'volume' => $volume,
                        'time' => $time * 1000,
                    ];

                    if ($value == '1day') {
                        //推送币种的日行情(带涨幅)
                        $change = $this->calcIncreasePair($kline_data);
                        bc_comp($change, 0) > 0 && $change = '+' . $change;
                        //追加涨幅等信息
                        $daymarket_data = [
                            'type' => 'daymarket',
                            'change' => $change,
                            'now_price' => $market_data['close'],
                            'api_form' => 'huobi_websocket',
                        ];
                        $kline_data = array_merge($kline_data, $daymarket_data);
                        //存入数据库
                        CurrencyQuotation::getInstance(1, $item->id)
                            ->updateData([
                                'change' => $daymarket_data['change'],
                                'now_price' => $kline_data['close'],
                                'volume' => $kline_data['volume'],
                            ]);
                        $now = microtime(true);
                        $params = [
                            'legal_id' => $kline_data['legal_id'],
                            'legal_name' => $kline_data['legal_name'],
                            'currency_id' => $kline_data['currency_id'],
                            'currency_name' => $kline_data['currency_name'],
                            'now_price' => $kline_data['close'],
                            'now' => $now
                        ];
                        //价格大于0才进行任务推送
                        if (bc_comp($kline_data['close'], 0) > 0) {
                            LeverUpdate::dispatch($params)->onQueue('lever:update');
                        }
                    }

                    echo "  ✅ 更新 {$item->name} 时间: {$time}\n";
//                    SendMarket::dispatch($kline_data)->onQueue('kline.all');
                    EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列
                }

                $successCount++;
            }

            // 批次完成，如果不是最后一批，则等待一下再处理下一批
            if ($currentBatch < $batchCount) {
                echo "批次 {$currentBatch} 完成，等待2秒后处理下一批...\n\n";
                sleep(2);
            }
        }

            echo "\n================================\n";
            echo "指定日期数据获取完成!\n";
            echo "指定日期: {$date}\n";
            echo "总币种数: {$totalCurrencies}\n";
            echo "成功处理: {$successCount}\n";
            echo "失败数量: {$errorCount}\n";

            if (!empty($errors)) {
                echo "\n错误详情:\n";
                foreach ($errors as $error) {
                    echo "- {$error}\n";
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => "指定日期 {$date} 的1day数据获取完成",
                'data' => [
                    'date' => $date,
                    'timestamp' => $specifyTimestamp,
                    'total_currencies' => $totalCurrencies,
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => $errors
                ]
            ]);
        }

        /**
         * 获取单个币种指定日期的1day数据
         * @param Request $request 包含 date 和 coin 参数
         * @return string
         */
        public function getTestSpecifyCoinDay(Request $request)
        {
            // 获取指定日期和币种参数
            $date = $request->input('date');
            $coin = $request->input('coin');

            if (empty($date)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '请提供日期参数，格式：Y-m-d，例如：2024-01-15'
                ]);
            }

            if (empty($coin)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '请提供币种参数，例如：BTCUSDT'
                ]);
            }

            // 验证日期格式
            $dateTime = \DateTime::createFromFormat('Y-m-d', $date);
            if (!$dateTime || $dateTime->format('Y-m-d') !== $date) {
                return response()->json([
                    'status' => 'error',
                    'message' => '日期格式错误，请使用 Y-m-d 格式，例如：2024-01-15'
                ]);
            }

            // 检查是否为未来日期
            $currentDate = date('Y-m-d');
            if ($date > $currentDate) {
                return response()->json([
                    'status' => 'error',
                    'message' => "不能查询未来日期的数据。当前日期: {$currentDate}，查询日期: {$date}"
                ]);
            }

            // 将指定日期转换为时间戳（当天结束时间）
            $specifyTimestamp = strtotime($date . '09:00:00');

            echo "开始获取币种 {$coin} 在指定日期 {$date} 的1day数据...\n";
            echo "指定时间戳: {$specifyTimestamp}\n";
            echo "API限流处理: 已启用请求延迟和重试机制\n";
            echo "================================\n";

            // 查询指定币种信息
            $item = DB::table('currency')
                ->leftJoin('currency_matches', 'currency.id', '=', 'currency_matches.currency_id')
                ->where('platform', '1')
                ->where('currency_matches.id', '<>', '')
                ->where('currency.name', strtoupper($coin))
                ->first(['currency.id', 'name', 'floating', 'currency_matches.id as match_id']);

            if (!$item) {
                return response()->json([
                    'status' => 'error',
                    'message' => "未找到币种 {$coin}，请检查币种名称是否正确"
                ]);
            }

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            echo "处理币种: {$item->name}...\n";

            try {
                // 使用带重试机制的方法获取指定日期的1day数据
                $datak = $this->curl_k($item->name, 8, 1, $specifyTimestamp);

                if ($datak === false) {
                    $errorCount++;
                    $errors[] = "币种 {$item->name}: API请求失败，跳过";
                    echo "  ❌ API请求失败，跳过\n";

                    return response()->json([
                        'status' => 'error',
                        'message' => "币种 {$coin} 的API请求失败",
                        'data' => [
                            'date' => $date,
                            'coin' => $coin,
                            'timestamp' => $specifyTimestamp,
                            'success_count' => 0,
                            'error_count' => 1,
                            'errors' => $errors
                        ]
                    ]);
                }
            } catch (\Throwable $e) {
                $errorCount++;
                $errors[] = "币种 {$item->name}: " . $e->getMessage();
                echo "  ❌ 错误: " . $e->getMessage() . "\n";

                return response()->json([
                    'status' => 'error',
                    'message' => "币种 {$coin} 处理异常: " . $e->getMessage(),
                    'data' => [
                        'date' => $date,
                        'coin' => $coin,
                        'timestamp' => $specifyTimestamp,
                        'success_count' => 0,
                        'error_count' => 1,
                        'errors' => $errors
                    ]
                ]);
            }

            $data_json = json_decode($datak);

            if (!isset($data_json->data->kline_list) || empty($data_json->data->kline_list)) {
                $errorCount++;
                $errors[] = "币种 {$item->name}: 无法获取指定日期的数据";
                echo "  ❌ 无法获取数据\n";

                return response()->json([
                    'status' => 'error',
                    'message' => "币种 {$coin} 在指定日期 {$date} 无可用数据",
                    'data' => [
                        'date' => $date,
                        'coin' => $coin,
                        'timestamp' => $specifyTimestamp,
                        'success_count' => 0,
                        'error_count' => 1,
                        'errors' => $errors
                    ]
                ]);
            }

            $data = $data_json->data->kline_list[0];

            $time = $data->timestamp;
            $open_price = $data->open_price;
            $close_price = $data->close_price;
            $high_price = $data->high_price;
            $low_price = $data->low_price;
            $volume = $data->volume;
            $symbol = strtoupper($item->name);

            // 应用浮动价格调整
            if ($item->floating != 0) {
                $open_price += $item->floating;
                $close_price += $item->floating;
                $high_price += $item->floating;
                $low_price += $item->floating;
            }

            $timeType = ['1day' => 4,];
            $periods = ['1day'];
            $change = '0.00'; // 初始化涨幅变量

            foreach ($periods as $value) {
                $time = $this->formatTimeline($timeType[$value], $time);

                // 检查ES中是否已存在该数据，如果存在则更新最高价和最低价
                $result = MarketHour::getEsearchMarketById(
                    $item->name,
                    'USDT',
                    $value,
                    $time
                );

                if (isset($result['_source'])) {
                    $origin_data = $result['_source'];

                    bc_comp($high_price, $origin_data['high']) < 0
                    && $high_price = $origin_data['high']; //新过来的价格如果不高于原最高价则不更新
                    bc_comp($low_price, $origin_data['low']) > 0
                    && $low_price = $origin_data['low']; //新过来的价格如果不低于原最低价则不更新
                }

                $market_data = [
                    'id' => $time,
                    'period' => $value,
                    'base-currency' => $item->name,
                    'quote-currency' => 'USDT',
                    'open' => sctonum($open_price),
                    'close' => sctonum($close_price),
                    'high' => sctonum($high_price),
                    'low' => sctonum($low_price),
                    'vol' => sctonum($volume),
                    'amount' => sctonum($close_price),
                ];

                $kline_data = [
                    'type' => 'kline',
                    'period' => $value,
                    'match_id' => $item->match_id,
                    'currency_id' => $item->id,
                    'currency_name' => $item->name,
                    'legal_id' => 1,
                    'legal_name' => 'USDT',
                    'open' => sctonum($open_price),
                    'close' => sctonum($close_price),
                    'high' => sctonum($high_price),
                    'low' => sctonum($low_price),
                    'symbol' => $item->name . '/' . 'USDT',
                    'volume' => $volume,
                    'time' => $time * 1000,
                ];

                if ($value == '1day') {
                    //推送币种的日行情(带涨幅)
                    $change = $this->calcIncreasePair($kline_data);
                    bc_comp($change, 0) > 0 && $change = '+' . $change;
                    //追加涨幅等信息
                    $daymarket_data = [
                        'type' => 'daymarket',
                        'change' => $change,
                        'now_price' => $market_data['close'],
                        'api_form' => 'huobi_websocket',
                    ];
                    $kline_data = array_merge($kline_data, $daymarket_data);
                    //存入数据库
                    CurrencyQuotation::getInstance(1, $item->id)
                        ->updateData([
                            'change' => $daymarket_data['change'],
                            'now_price' => $kline_data['close'],
                            'volume' => $kline_data['volume'],
                        ]);
                    $now = microtime(true);
                    $params = [
                        'legal_id' => $kline_data['legal_id'],
                        'legal_name' => $kline_data['legal_name'],
                        'currency_id' => $kline_data['currency_id'],
                        'currency_name' => $kline_data['currency_name'],
                        'now_price' => $kline_data['close'],
                        'now' => $now
                    ];
                    //价格大于0才进行任务推送
                    if (bc_comp($kline_data['close'], 0) > 0) {
                        LeverUpdate::dispatch($params)->onQueue('lever:update');
                    }
                }

                echo "  ✅ 成功更新 {$item->name} 时间: {$time}\n";
                echo "  📊 价格信息: 开盘={$open_price}, 收盘={$close_price}, 最高={$high_price}, 最低={$low_price}\n";
                echo "  📈 涨幅: {$change}%\n";

//                SendMarket::dispatch($kline_data)->onQueue('kline.all');
                EsearchMarket::dispatch($market_data)->onQueue('esearch:market');//统一用一个队列
            }

            $successCount++;

            echo "\n================================\n";
            echo "单币种指定日期数据获取完成!\n";
            echo "币种: {$coin}\n";
            echo "指定日期: {$date}\n";
            echo "处理状态: 成功\n";

            return response()->json([
                'status' => 'success',
                'message' => "币种 {$coin} 在指定日期 {$date} 的1day数据获取完成",
                'data' => [
                    'date' => $date,
                    'coin' => $coin,
                    'timestamp' => $specifyTimestamp,
                    'formatted_time' => $time,
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'market_data' => $market_data,
                    'price_info' => [
                        'open' => $open_price,
                        'close' => $close_price,
                        'high' => $high_price,
                        'low' => $low_price,
                        'volume' => $volume,
                        'change' => $change
                    ]
                ]
            ]);
        }



        public function queueTest()
        {

            $kline_data = [
                'type' => 'kline',
                'period' => '1min',
                'match_id' => 1,
                'currency_id' => 2,
                'currency_name' => 'BTC',
                'legal_id' => 1,
                'legal_name' => 'USDT',
                'open' => sctonum(100),
                'close' => sctonum(100),
                'high' => sctonum(100),
                'low' => sctonum(100),
                'symbol' => 'BTC' . '/' . 'USDT',
                'volume' => sctonum(100),
                'time' => time() * 1000,
            ];
            UserChat::sendText($kline_data);

        }


        public function formatTimeline($type, $day_time = null)
        {
            empty($day_time) && $day_time = time();
            switch ($type) {
                //15分钟
                case 1:
                    $start_time = strtotime(date('Y-m-d H:00:00', $day_time));
                    $minute = intval(date('i', $day_time));
                    $multiple = floor($minute / 15);
                    $minute = $multiple * 15;
                    $time = $start_time + $minute * 60;

                    break;
                //1小时
                case 2:
                    $time = strtotime(date('Y-m-d H:00:00', $day_time));
                    break;
                //4小时
                case 3:
                    $start_time = strtotime(date('Y-m-d', $day_time));
                    $hours = intval(date('H', $day_time));
                    $multiple = floor($hours / 4);
                    $hours = $multiple * 4;
                    $time = $start_time + $hours * 3600;
                    break;
                //一天
                case 4:
                    $time = strtotime(date('Y-m-d', $day_time));
                    break;
                //分时
                case 5:
                    $time_string = date('Y-m-d H:i', $day_time);
                    $time = strtotime($time_string);
                    break;
                //5分钟
                case 6:
                    $start_time = strtotime(date('Y-m-d H:00:00', $day_time));
                    $minute = intval(date('i', $day_time));
                    $multiple = floor($minute / 5);
                    $minute = $multiple * 5;
                    $time = $start_time + $minute * 60;
                    break;
                //30分钟
                case 7:
                    $start_time = strtotime(date('Y-m-d H:00:00', $day_time));
                    $minute = intval(date('i', $day_time));
                    $multiple = floor($minute / 30);
                    $minute = $multiple * 30;
                    $time = $start_time + $minute * 60;
                    break;
                //一周
                case 8:
                    $start_time = strtotime(date('Y-m-d', $day_time));
                    $week = intval(date('w', $day_time));
                    $diff_day = $week;
                    $time = $start_time - $diff_day * 86400;
                    break;
                //一月
                case 9:
                    $time_string = date('Y-m', $day_time);
                    $time = strtotime($time_string);
                    break;
                //一年
                case 10:
                    $time = strtotime(date('Y-01-01', $day_time));
                    break;
                default:
                    $time = $day_time;
                    break;
            }
            return $time;
        }


   /*     protected function calcIncreasePair($kline_data)
        {
            //dd($kline_data);
            $open = $kline_data['open'];
            $close = $kline_data['close'];;
            $change_value = bc_sub($close, $open);
            $change = bc_mul(bc_div($change_value, $open), 100, 3);
            return $change;
        }*/

        protected function calcIncreasePair($kline_data)
        {

            $open = $kline_data['open'];
            $close = $kline_data['close'];;
            $change_value = bc_sub($close, $open);
            $change = bc_mul(bc_div($change_value, $open), 100, 2);
            return $change;
        }

        public function curl($path, $query)
        {
            $host = "http://**************";
            $method = "GET";
            $appcode = $this->code;
            $headers = array();
            // array_push($headers, "Authorization:APPCODE " . $appcode);
            $url = $host . $path . "?" . "username=lklee&password=004d6139e67ad1a9b3833061abc562fd&column=price,open,high,low,vol&id=" . $query;

            $res = file_get_contents($url);

            return $res;
        }


        public function curl_k($query, $period, $num = 1, $kline_timestamp_end = 0)
        {

            if ($query == 'XAGUSD') {
                $query = 'Silver';
            }
            $host = "https://quote.tradeswitcher.com/quote-b-api/kline";
            $method = "GET";
            $query = "{'trace':'cafab691df4a7ab1823bd6cb48759806-c-app','data':{'code':'" . $query . "','kline_type':" . $period . ",'kline_timestamp_end':" . $kline_timestamp_end . ",'query_kline_num':" . $num . ",'adjust_type':0}}";

            $url = $host . "?" . "token=cafab691df4a7ab1823bd6cb48759806-c-app&query=" . $query;

            $res = file_get_contents($url);

            return $res;
        }


        public function object2array($obj)
        {
            return json_decode(json_encode($obj), true);
        }

        public function get_history_kline($symbol = '', $period = '', $size = 0)
        {
            echo "获取K线数据\r\n";
            $this->api_method = "/market/history/kline";
            $this->req_method = 'GET';
            $param = ['symbol' => $symbol, 'period' => $period];
            if ($size) {
                $param['size'] = $size;
            }
            $url = $this->create_sign_url($param);
            file_put_contents("log.txt", $url . PHP_EOL, FILE_APPEND);
            echo "获取K线数据结束\r\n";
            return json_decode($this->curls($url), true);
        }

        public function create_sign_url($append_param = [])
        {
            $param = ['AccessKeyId' => ACCESS_KEY, 'SignatureMethod' => 'HmacSHA256', 'SignatureVersion' => 2, 'Timestamp' => date('Y-m-d\\TH:i:s', time())];
            if ($append_param) {
                foreach ($append_param as $k => $ap) {
                    $param[$k] = $ap;
                }
            }
            return $this->url . $this->api_method . '?' . $this->bind_param($param);
        }

        public function bind_param($param)
        {
            $u = [];
            $sort_rank = [];
            foreach ($param as $k => $v) {
                $u[] = $k . "=" . urlencode($v);
                $sort_rank[] = ord($k);
            }
            asort($u);
            $u[] = "Signature=" . urlencode($this->create_sig($u));
            return implode('&', $u);
        }

        public function create_sig($param)
        {
            $sign_param_1 = $this->req_method . "\r\n" . $this->api . "\r\n" . $this->api_method . "\r\n" . implode('&', $param);
            $signature = hash_hmac('sha256', $sign_param_1, SECRET_KEY, true);
            return base64_encode($signature);
        }

        public function curls($url, $postdata = [])
        {
            echo "curl开始\r\n";
            $start = microtime(true);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            if ($this->req_method == 'POST') {
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postdata));
            }
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_TIMEOUT, 4);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/json"]);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
            $output = curl_exec($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);
            if (empty($output)) {
                echo "curl没有采集到\r\n";
            }
            echo "curl结束\r\n";
            $end = microtime(true);
            // file_put_contents("haoshi.txt", $end - $start . PHP_EOL, FILE_APPEND);
            return $output;
        }
    }
