<?php

namespace App\Nova\Actions;


use App\Models\AccountLog;
use App\Models\Currency;
use App\Models\Setting;
use App\Models\Users;
use App\Models\UsersWallet;
use App\Models\UserLevelModel;
use App\Models\UsersWalletOut;
use Illuminate\Bus\Queueable;
use Illuminate\Cache\RedisLock;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use App\Models\ChargeReq;
use PHPMailer\PHPMailer\PHPMailer;


class ExtractRefuse extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = '驳回';

    /**
     * Perform the action on the given models.
     *
     * @param \Laravel\Nova\Fields\ActionFields $fields
     * @param \Illuminate\Support\Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
         
            if ($model->status <> 1) {
                return Action::danger('无法操作');
            }
            $id = $model->id;
            $notes=$fields->notes;
            if (!$id) {
                return Action::danger('参数错误');
            }
            $user = Users::getById($model->user_id);
            //dd($user);
            try {
                DB::beginTransaction();
                $wallet_out = UsersWalletOut::where('status', '<=', 1)->lockForUpdate()->findOrFail($id);
                $number = $wallet_out->number;
                $user_id = $wallet_out->user_id;
                $user_wallet = UsersWallet::where('user_id', $user_id)->where('currency', 1)->lockForUpdate()->first();
                $wallet_out->status = 3;//提币失败状态
                $wallet_out->notes = $notes;//反馈的信息

                $wallet_out->update_time = time();
                $wallet_out->save();
                $change_result = change_wallet_balance($user_wallet, 2, -$number, AccountLog::WALLETOUTBACK, '提币失败,锁定余额减少', true);
                if ($change_result !== true) {
                    return Action::danger($change_result);
                }
                $change_result = change_wallet_balance($user_wallet, 2, $number, AccountLog::WALLETOUTBACK, '提币失败,锁定余额撤回');
                if ($change_result !== true) {
                    return Action::danger($change_result);
                }
                //发送邮件
                //  从设置中取出值
                $username = Setting::getValueByKey('phpMailer_username', '');
                $host = Setting::getValueByKey('phpMailer_host', '');
                $password = Setting::getValueByKey('phpMailer_password', '');
                $port = Setting::getValueByKey('phpMailer_port', 465);
                $mail_from_name = Setting::getValueByKey('submail_from_name', '');
                $mail = new PHPMailer(true);
                $mail->isSMTP();
                $mail->CharSet = "utf-8";
                $mail->SMTPAuth = true;
                $mail->SMTPSecure = "ssl";
                $mail->Host = $host;
                $mail->Port = 465;//$port;
                $mail->Username = $username;
                $mail->Password = $password;//去开通的qq或163邮箱中找,这里用的不是邮箱的密码，而是开通之后的一个token
//            $mail->SMTPDebug = 2; //用于debug PHPMailer信息
                $mail->setFrom($username, $mail_from_name);//设置邮件来源  //发件人
                $mail->Subject = "Deposit coins faile"; //邮件标题
                $mail->MsgHTML("Your withdrawal has been rejected");
                $mail->addAddress($user->email);  //收件人（用户输入的邮箱）
                $mail->send();
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                return Action::danger($e->getMessage());
            }
        }
        return Action::message('操作成功');
    }

    /**
     * Get the fields available on the action.
     *
     * @param \Laravel\Nova\Http\Requests\NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make(__('Notes'), 'notes'),
        ];
    }


}
