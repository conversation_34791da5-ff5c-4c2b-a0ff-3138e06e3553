<?php

namespace App\Nova\Currency;

use App\Nova\Resource;
use App\Nova\Actions\EmailAccountProfile;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class CurrencyKx extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Currency\CurrencyKx>
     */
    public static $model = \App\Models\CurrencyKx::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];
    


    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('DealCurrency'),'currency', 'App\Nova\Currency\Currency'),
            DateTime::make(__('dataStart'),'data_start'),
            DateTime::make(__('dataEnd'),'data_end'),
            // DateTime::make(__('putStart'),'put_start'),
            // DateTime::make(__('putEnd'),'put_end'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [ (new EmailAccountProfile)->showInline()];
    }
}
